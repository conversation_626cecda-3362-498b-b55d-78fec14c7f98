livekit:
    docker run --rm --tty \
    --publish 127.0.0.1:7880:7880/tcp \
    --publish 127.0.0.1:7881:7881/tcp \
    --publish 127.0.0.1:7882:7882/udp \
    docker.io/livekit/livekit-server:latest --dev --bind 0.0.0.0

edge_tts:
    docker run --rm --tty \
    --publish 127.0.0.1:5050:5050/tcp \
    --env REQUIRE_API_KEY=False \
    docker.io/travisvn/openai-edge-tts:latest

transformers_serve:
    uv run --group dev transformers serve

set unstable := true

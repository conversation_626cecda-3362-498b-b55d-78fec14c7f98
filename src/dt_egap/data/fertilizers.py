from dataclasses import dataclass
from typing import List
from enum import Enum
from src.dt_egap.models.farm_records import FertilizerType

@dataclass
class FertilizerData:
    name: str
    category: FertilizerType
    stage: str
    rate: str
    purpose: str

class FertilizerDataDB:
    def __init__(self):
        self.data = [
            # Chemical Fertilizers
            FertilizerData("Urea (46-0-0)", FertilizerType.CHEMICAL, "Leaf flush stage", "50g/tree or foliar", "Stimulate leaf growth"),
            FertilizerData("Ammonium nitrate (34-0-0)", FertilizerType.CHEMICAL, "Vegetative stage", "soil application", "Improve vegetative vigor"),
            FertilizerData("Calcium ammonium nitrate", FertilizerType.CHEMICAL, "Pre-flower", "200g/tree", "N-rich boost for growth"),
            FertilizerData("Calcium nitrate", FertilizerType.CHEMICAL, "Foliar spray", "10g/20L", "Prevent calcium deficiency"),
            FertilizerData("Mono-potassium phosphate (MKP)", FertilizerType.CHEMICAL, "Flowering", "10-20g/20L foliar", "Boost flowering & root growth"),
            FertilizerData("Di-ammonium phosphate (DAP)", FertilizerType.CHEMICAL, "Pre-bloom", "100g/tree", "Provide P for flowering"),
            FertilizerData("Potassium sulfate (K2SO4)", FertilizerType.CHEMICAL, "Fruit development", "500g/tree", "Improve fruit size & sugar"),
            FertilizerData("Potassium nitrate (KNO3)", FertilizerType.CHEMICAL, "Early fruiting", "200g/20L foliar", "Promote flower retention"),
            FertilizerData("NPK 15-15-15", FertilizerType.CHEMICAL, "Post-harvest", "2kg/tree", "Replenish nutrients post-harvest"),
            FertilizerData("NPK 0-30-20 or 10-60-10", FertilizerType.CHEMICAL, "Flowering induction", "500g/tree", "High P/K for flower induction"),
            
            # Organic Fertilizers
            FertilizerData("Chicken manure", FertilizerType.ORGANIC, "Base dressing", "10-20kg/tree", "Organic NPK source"),
            FertilizerData("Cow manure", FertilizerType.ORGANIC, "Base dressing", "10-20kg/tree", "Improve soil structure"),
            FertilizerData("Composted manure (livestock)", FertilizerType.ORGANIC, "Soil improvement", "10kg/tree", "Long-term fertility"),
            FertilizerData("Green manure", FertilizerType.ORGANIC, "Cover crop phase", "", "Nitrogen fixation"),
            FertilizerData("Humic acid", FertilizerType.ORGANIC, "Soil & foliar", "20ml/20L", "Enhance nutrient uptake"),
            FertilizerData("Seaweed extract (kelp)", FertilizerType.ORGANIC, "Foliar", "20ml/20L", "Promote flowering"),
            FertilizerData("Amino acid-based liquid", FertilizerType.ORGANIC, "Foliar feeding", "20-50ml/20L", "Support growth under stress"),
            FertilizerData("Bacillus subtilis (biofertilizer)", FertilizerType.ORGANIC, "Soil drench", "100ml/tree", "Boost soil microbe health"),
            FertilizerData("Vinasse (molasses residue)", FertilizerType.ORGANIC, "Soil drench", "2-5L/tree", "Organic K, micronutrients"),
            FertilizerData("Silkworm excrement", FertilizerType.ORGANIC, "Soil dressing", "5kg/tree", "Natural nutrient source"),
            
            # Plant Growth Regulators
            FertilizerData("Paclobutrazol", FertilizerType.PLANT_GROWTH_REGULATOR, "Off-season flower induction", "2000-2500ppm", "Induce off-season flowering"),
            FertilizerData("Uniconazole", FertilizerType.PLANT_GROWTH_REGULATOR, "Growth control", "50-100ppm foliar", "Control excessive growth"),
            FertilizerData("Dikegulac sodium", FertilizerType.PLANT_GROWTH_REGULATOR, "Lateral branching", "100-200ppm", "Promote lateral shoots"),
            FertilizerData("Gibberellic acid (GA3)", FertilizerType.PLANT_GROWTH_REGULATOR, "Flower set", "50-100ppm spray", "Stimulate flowering/elongation"),
            FertilizerData("Naphthaleneacetic acid (NAA)", FertilizerType.PLANT_GROWTH_REGULATOR, "Fruit set", "20-50ppm spray", "Enhance fruit set"),
            FertilizerData("Salicylic acid (SA)", FertilizerType.PLANT_GROWTH_REGULATOR, "Stress mitigation", "100ppm foliar", "Enhance stress tolerance"),
            FertilizerData("Seaweed extract", FertilizerType.PLANT_GROWTH_REGULATOR, "Biostimulant", "20ml/20L foliar", "Support flower and fruit set"),
            FertilizerData("Humic acid (biostimulant)", FertilizerType.PLANT_GROWTH_REGULATOR, "Root/leaf growth", "10-30ml/20L", "Boost root/shoot dev."),
            FertilizerData("Amino acid fertilizers", FertilizerType.PLANT_GROWTH_REGULATOR, "Flowering support", "30ml/20L foliar", "Stimulate metabolism"),
            FertilizerData("Chelated iron (EDDHA-Fe)", FertilizerType.PLANT_GROWTH_REGULATOR, "Iron deficiency", "20g/tree foliar", "Correct chlorosis & improve sweetness"),
        ]
    
    def by_category(self, cat: FertilizerType) -> List[FertilizerData]:
        return [p for p in self.data if p.category == cat]
    
    def by_stage(self, stage: str) -> List[FertilizerData]:
        return [p for p in self.data if stage.lower() in p.stage.lower()]
    
    def search(self, keyword: str) -> List[FertilizerData]:
        keyword = keyword.lower()
        return [p for p in self.data if keyword in p.name.lower() or 
                keyword in p.stage.lower() or keyword in p.purpose.lower()]
    
    def flowering(self) -> List[FertilizerData]:
        keywords = ["flower", "bloom", "flowering"]
        result = []
        for k in keywords:
            result.extend(self.by_stage(k))
        return list(set(result))
    
    def foliar(self) -> List[FertilizerData]:
        return [p for p in self.data if "foliar" in p.rate.lower() or "foliar" in p.stage.lower()]

# Usage
if __name__ == "__main__":
    db = FertilizerDataDB()
    
    # Examples
    chemicals = db.by_category(FertilizerType.CHEMICAL)
    pgrs = db.by_category(FertilizerType.PGR)
    flowering_FertilizerDatas = db.flowering()
    foliar_FertilizerDatas = db.foliar()
    potassium_FertilizerDatas = db.search("potassium")
    
    print(f"Total FertilizerDatas: {len(db.data)}")
    print(f"Chemical fertilizers: {len(chemicals)}")
    print(f"PGRs: {len(pgrs)}")
    print(f"Flowering FertilizerDatas: {len(flowering_FertilizerDatas)}")

# Quick access dict for common needs
QUICK_REFS = {
    "off_season": "Paclobutrazol",
    "foliar_N": "Urea (46-0-0)",
    "flowering": "MKP",
    "fruit_quality": "Potassium sulfate",
    "base_organic": "Chicken manure"
}
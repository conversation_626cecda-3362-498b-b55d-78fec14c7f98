from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List, Literal
from enum import Enum
from pydantic import BaseModel, Field


class RecordType(Enum):
    FERTILIZER = "fertilizer"
    CHEMICAL = "chemical"
    HARVESTING = "harvesting"


class ProductionFactorCategory(Enum):
    FERTILIZER = "fertilizer"
    CHEMICAL = "chemical"


class FertilizerType(Enum):
    ORGANIC = "organic"
    CHEMICAL = "chemical"
    PLANT_GROWTH_REGULATOR = "plant_growth_regulator"
    OTHER = "other"


@dataclass
class FertilizerRecord:
    """Records for fertilizer usage"""
    record_id: int
    fertilizer_type: FertilizerType
    type_formula_name: str
    purchase_date: date
    source_of_purchase: str
    rate_of_use: float
    usage_date: date
    operator_name: str
    nth_record: int = 0
    record_date: datetime = None

    def __post_init__(self):
        if self.record_date is None:
            # self.record_date = datetime.now()
            pass


@dataclass
class ChemicalRecord:
    """Records for pesticide usage following agricultural hazardous substances form"""
    trade_name: str  # Trade name
    common_name: str  # Common name
    registration_of_hazardous_substances: str  # Registration of Hazardous Substances
    mfg_exp_date: str  # Mfg / Exp Date
    purchase_date: date  # Purchase date
    source_of_purchase: str  # Where purchased
    application_date: date  # Date Applied (Chemical)
    rate_of_use: float  # Rate of use: per 20 liters of water / per rai
    total_volume: float  # Total volume used
    worker_name: str  # Worker's Name
    nth_record: int = 0
    record_date: datetime = None
    pest_type: Optional[str] = None

    def __post_init__(self):
        if self.record_date is None:
            # self.record_date = datetime.now()
            pass


@dataclass
class HarvestRecord:
    record_id: int
    crop_type: str
    quantity_kg: float
    harvest_date: date
    harvested_by: Literal["self", "buyer", "hired_labor"]
    buyer_name: Optional[str] = None
    transport_vehicle: Optional[str] = None
    selling_price_per_kg: Optional[float] = None
    record_date: datetime = None
    nth_record: int = 0

    def __post_init__(self):
        if self.record_date is None:
            # self.record_date = datetime.now()
            pass


@dataclass
class FarmRecords:
    fertilizer_records: List["FertilizerRecord"]
    chemical_records: List["ChemicalRecord"]
    harvest_records: List["HarvestRecord"]

    def __init__(self):
        self.fertilizer_records = []
        self.chemical_records = []
        self.harvest_records = []

    def add_fertilizer_record(self, record: "FertilizerRecord"):
        """Add a fertilizer record"""
        self.fertilizer_records.append(record)

    def add_chemical_record(self, record: "ChemicalRecord"):
        """Add a chemical record"""
        self.chemical_records.append(record)

    def add_harvest_record(self, record: "HarvestRecord"):
        """Add a harvest record"""
        self.harvest_records.append(record)

    def get_crop_harvest_count(self, crop_type: str) -> int:
        """Get number of harvest records for a specific crop"""
        return len(
            [
                r
                for r in self.harvest_records
                if r.crop_type.lower() == crop_type.lower()
            ]
        )

    def get_total_crop_production(self, crop_type: str) -> float:
        """Get total production quantity for a specific crop"""
        return sum(
            r.quantity_kg
            for r in self.harvest_records
            if r.crop_type.lower() == crop_type.lower()
        )

    def has_recorded_fertilizer(self, fertilizer_name: str) -> bool:
        """Check if a fertilizer has been recorded before"""
        for record in self.fertilizer_records:
            if record.type_formula_name.lower() == fertilizer_name.lower():
                return True
        return False

    def has_recorded_chemical(self, chemical_name: str) -> bool:
        """Check if a chemical has been recorded before (by trade name or common name)"""
        for record in self.chemical_records:
            if (
                record.trade_name.lower() == chemical_name.lower()
                or record.common_name.lower() == chemical_name.lower()
            ):
                return True
        return False
    
    def find_fertilizer_record(self, fertilizer_name: str) -> Optional['FertilizerRecord']:
        """Find existing fertilizer record by name"""
        for record in self.fertilizer_records:
            if record.type_formula_name.lower() == fertilizer_name.lower():
                return record
        return None

    def find_chemical_record(self, chemical_name: str) -> Optional["ChemicalRecord"]:
        """Find existing chemical record by name (by trade name or common name)"""
        for record in self.chemical_records:
            if (
                record.trade_name.lower() == chemical_name.lower()
                or record.common_name.lower() == chemical_name.lower()
            ):
                return record
        return None

    def get_all_fertilizer_records(self) -> List["FertilizerRecord"]:
        """Get all fertilizer records"""
        return self.fertilizer_records

    def get_all_chemical_records(self) -> List["ChemicalRecord"]:
        """Get all chemical records"""
        return self.chemical_records
    
    def get_all_fertilizer_records_count(self) -> int:
        """Get total count of fertilizer records"""
        return len(self.fertilizer_records)
    
    def get_all_chemical_records_count(self) -> int:
        """Get total count of chemical records"""
        return len(self.chemical_records)
    
    def get_fertilizer_record_by_id(self, record_id: int) -> Optional["FertilizerRecord"]:
        """Get fertilizer record by ID"""
        for record in self.fertilizer_records:
            if record.record_id == record_id:
                return record
        return None
    
    def update_fertilizer_record(self, record_id: int, updated_record: "FertilizerRecord") -> bool:
        """Update an existing fertilizer record by ID"""
        for i, record in enumerate(self.fertilizer_records):
            if record.record_id == record_id:
                self.fertilizer_records[i] = updated_record
                return True
        return False
    
    def get_harvest_record_by_id(self, record_id: int) -> Optional["HarvestRecord"]:
        """Get harvest record by ID"""
        for record in self.harvest_records:
            if record.record_id == record_id:
                return record
        return None
    
    def update_harvest_record(self, record_id: int, updated_record: "HarvestRecord") -> bool:
        """Update an existing harvest record by ID"""
        for i, record in enumerate(self.harvest_records):
            if record.record_id == record_id:
                self.harvest_records[i] = updated_record
                return True
        return False


@dataclass
class ConversationContext:
    current_record_type: Optional[RecordType] = None
    partial_fertilizer: Optional[dict] = None
    partial_chemical: Optional[dict] = None
    partial_harvest: Optional[dict] = None
    awaiting_confirmation: bool = False
    last_user_input: str = ""
    conversation_step: str = "greeting"



class FertilizerUsageSchema(BaseModel):
 
    fertilizer_type: Optional[Literal["ORGANIC", "CHEMICAL", "PLANT_GROWTH_REGULATOR", "OTHER"]] = Field(
        None,
        description="Category of fertilizer: ORGANIC for compost/manure, CHEMICAL for synthetic NPK/urea"
    )
    
    fertilizer_name: str = Field(
        ...,
        description="Specific brand or product name mentioned by farmer (e.g. 'NPK 15-15-15', 'Urea 46%', 'ปุ่ยโคเดง')"
    )
    
    quantity_kg_per_rai: Optional[float] = Field(
        None,
        description="Amount of fertilizer applied per rai in kilograms. Convert other units: 1 bag usually = 25-50kg, 1 quintal = 100kg"
    )
    
    application_date: Optional[date] = Field(
        None,
        description="Date when fertilizer was applied to field. Use YYYY-MM-DD format. If farmer says 'yesterday', 'last week', calculate actual date"
    )
    
    purchase_date: Optional[date] = Field(
        None,
        description="Date when fertilizer was purchased from supplier. Use YYYY-MM-DD format"
    )
    
    applied_by: Optional[str] = Field(
        None,
        description="Name of person who applied fertilizer to the field (could be farmer's name, worker name, or 'self')"
    )
    
    source_of_purchase: Optional[str] = Field(
        None,
        description="Store, dealer, or supplier where fertilizer was purchased"
    )


class HarvestRecordingSchema(BaseModel):
    """Schema for recording harvest activities"""
    
    crop_type: str = Field(
        description="Type of crop harvested as mentioned by farmer (e.g. 'rice', 'corn', 'cassava', 'vegetables', 'ข้าว', 'ข้าวโพด', 'มัน'). Use farmer's exact terminology"
    )
    
    harvest_quantity: Optional[float] = Field(
        None,
        description="Total quantity harvested in kilograms. Convert units: 1 ton = 1000kg, 1 quintal = 100kg, traditional Thai units as appropriate"
    )
    
    harvest_date: Optional[date] = Field(
        None,
        description="Date when crop was harvested from field. Use YYYY-MM-DD format. Parse relative dates like 'last month', 'this week', 'tomorrow'"
    )
    
    harvest_by: Optional[Literal["self", "buyer", "hired labor"]] = Field(
        None,
        description="Who performed the harvesting work: 'self' if farmer did it, 'buyer' if purchaser harvested, 'hired labor' if workers were employed"
    )
    
    buyer_name: Optional[str] = Field(
        None,
        description="Name of person/company who purchased the harvest (individual buyer, trader name, company name, or 'local market')"
    )
    
    transport_vehicle: Optional[str] = Field(
        None,
        description="Vehicle used to transport harvest (truck, pickup, motorcycle, tractor, buffalo cart). Describe what farmer mentions"
    )
    
    selling_price_per_kg: Optional[float] = Field(
        None,
        description="Price received per kilogram in local currency (Thai Baht). Convert from other units like per ton, per bag if mentioned"
    )


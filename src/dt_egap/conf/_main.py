from dataclasses import dataclass, field
from typing import Any

import omegaconf
from hydra_zen import BuildsFn, store
from hydra_zen.typing import ZenPartialBuilds
from livekit import agents
from livekit.plugins import openai, silero
from dt_egap.plugins.vns_tts.google_tts import CustomGoogleTTS
from dt_egap.plugins.vns_tts.newline_tokenizer import CustomSentenceTokenizer
from livekit.plugins import google, aws

from dotenv import load_dotenv
import json
import base64
import os

# load google credentials
load_dotenv()
LANGUAGE = os.getenv("LANGUAGE", "en") # en, th

if LANGUAGE == "th":
    google_language = "th-TH"
    google_voice = "th-TH-Chirp3-HD-Achernar"
    edge_voice = "th-TH-NiwatNeural"
    edge_model = "tts-1"

else:
    google_language = "en-US"
    google_voice = "en-US-Chirp-HD-F"
    edge_voice = "en-US-AriaNeural"
    edge_model = "tts-1"

print(f"Language: {LANGUAGE}")
print(f"Google_language: {google_language}")
print(f"Google_voice: {google_voice}")
print(f"Edge_voice: {edge_voice}")
print(f"Edge_model: {edge_model}")

try:
    gg_creds = dict(json.loads(base64.b64decode(os.getenv("GOOGLE_CRE")).decode("utf-8")))
except Exception as e:
    try: # TODO: add google credentials from file
        gg_creds = dict(json.loads(base64.b64decode(os.getenv("GOOGLE_CRE_FILE").decode("utf-8"))))
    except Exception as e:
        print(f"Error loading google credentials: {e}")
        gg_creds = None
finally:
    print(f"gg_creds: {gg_creds}")



class Builds(BuildsFn):
    @classmethod
    def _make_hydra_compatible(cls, value: Any, **kwargs):
        if isinstance(value, agents.types.NotGiven):
            return omegaconf.MISSING
        return super()._make_hydra_compatible(value, **kwargs)


builds = Builds.builds


@store
@dataclass
class Conf:
    defaults: list[str | dict[str, str | list[str] | None]] = field(
        default_factory=lambda: [
            "_self_",
            {"stt_group@session_partial.stt": "google"},  # transformers
            {"llm_group@session_partial.llm": "bedrock-claude-4"},
            {"tts_group@session_partial.tts": "google_custom"},
        ]
    )
    session_partial: ZenPartialBuilds[agents.AgentSession] = builds(
        agents.AgentSession,
        vad=builds(silero.VAD.load),
        allow_interruptions=False,
        zen_partial=True,
        populate_full_signature=True,
        # allow_interruptions=False,
    )
    # database: Builds[Database] = DBConf


### STT

stt_group = store(group="stt_group")
stt_group(
    builds(
        openai.STT,
        base_url="http://localhost:8000/v1",
        api_key="",
        model="openai/whisper-large-v3-turbo",
        noise_reduction_type="near_field",
        # detect_language=False,
    ),
    name="transformers",
)

stt_group(
    builds(
        google.STT,
        languages=google_language,  # en-US, th-TH
        detect_language=False,
        credentials_info=gg_creds,
        model="latest_long",  # latest_long, latest_short
        spoken_punctuation=False,
        punctuate=False,
        # detect_language=False,
        use_streaming=True,
    ),
    name="google",
)

### LLM

llm_group = store(group="llm_group")
llm_group(
    builds(
        openai.LLM,
        base_url="http://localhost:1234/v1",
        api_key="",
        model="liquid/lfm2-1.2b",
    ),
    name="lms",
)

llm_group(
    builds(
        aws.LLM,
        model="apac.anthropic.claude-sonnet-4-20250514-v1:0",
        region="ap-southeast-1"
    ),
    name="bedrock-claude-4",
)

### TTS

tts_group = store(group="tts_group")
tts_group(
    builds(
        openai.TTS,
        base_url="http://localhost:5050/v1",
        api_key="",
        model="tts-1",
        voice="th-TH-NiwatNeural",
    ),
    name="edge",
)


tts_group(
    builds(
        google.TTS,
        language=google_language,  # th-TH, en-US
        voice_name=google_voice,  # th-TH-Chirp3-HD-Achernar, en-US-Chirp-HD-F
        gender="neutral",
        credentials_info=gg_creds,
    ),
    name="google",
)

tokenizer = builds(CustomSentenceTokenizer, min_sentence_len=1, stream_context_len=1)

if LANGUAGE == "th":
    tts_group(
        builds(
            CustomGoogleTTS,
            language=google_language,
            voice_name=google_voice,
            gender="neutral",
            credentials_info=gg_creds,
            tokenizer=tokenizer
        ),
        name="google_custom",
    )
elif LANGUAGE == "en":
    tts_group(
        builds(
            google.TTS,
            language=google_language,
            voice_name=google_voice,
            gender="neutral",
            credentials_info=gg_creds,
        ),
        name="google_custom",
    )

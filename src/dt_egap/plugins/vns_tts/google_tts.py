from livekit.agents.types import APIConnectOptions, NOT_GIVEN
from livekit.agents import tts, utils

from livekit.plugins.google.tts import (
    TTS as GoogleTTS,
    SynthesizeStream as GoogleSynthesizeStream
)
from google.cloud import texttospeech

from loguru import logger
import weakref
import asyncio
from typing import AsyncGenerator
from google.api_core.exceptions import DeadlineExceeded, GoogleAPICallError
from livekit.agents import tokenize
from livekit.agents import APIStatusError, APITimeoutError


    
class CustomSynthesizeStream(GoogleSynthesizeStream):

    async def _run_stream(
        self,
        input_stream: tokenize.SentenceStream,
        output_emitter: tts.AudioEmitter,
        streaming_config: texttospeech.StreamingSynthesizeConfig,
    ) -> None:
        @utils.log_exceptions(logger=logger)

        async def input_generator() -> AsyncGenerator[texttospeech.StreamingSynthesizeRequest, None]:
            try:
                yield texttospeech.StreamingSynthesizeRequest(streaming_config=streaming_config)
                
                heartbeat_interval = 4.5
                input_iterator = input_stream.__aiter__()
                
                while True:
                    try:
                        # Wait for next input with timeout for heartbeat
                        input_item = await asyncio.wait_for(
                            input_iterator.__anext__(), 
                            timeout=heartbeat_interval
                        )
                        self._mark_started()
                        yield texttospeech.StreamingSynthesizeRequest(
                            input=texttospeech.StreamingSynthesisInput(text=input_item.token)
                        )
                        
                    except asyncio.TimeoutError:
                        logger.info("Sending heartbeat to keep TTS connection alive.")
                        yield texttospeech.StreamingSynthesizeRequest(
                            input=texttospeech.StreamingSynthesisInput(text="")
                        )
                        
                    except StopAsyncIteration:
                        break
            except Exception:
                logger.exception("an error occurred while streaming input to google TTS")
                    

        input_gen = input_generator()
        
        try:
            stream = await self._tts._ensure_client().streaming_synthesize(
                input_gen, timeout=self._conn_options.timeout
            )
            output_emitter.start_segment(segment_id=utils.shortuuid())

            async for resp in stream:
                output_emitter.push(resp.audio_content)

            output_emitter.end_segment()

        except DeadlineExceeded:
            raise APITimeoutError() from None
        except GoogleAPICallError as e:
            raise APIStatusError(e.message, status_code=e.code or -1) from e
        finally:
            await input_gen.aclose()

class CustomGoogleTTS(GoogleTTS):

    def stream(
        self, *, conn_options: APIConnectOptions = None
    ) -> CustomSynthesizeStream:
        """Override to return custom stream."""
        if conn_options is None:
            from livekit.agents.types import DEFAULT_API_CONNECT_OPTIONS
            conn_options = DEFAULT_API_CONNECT_OPTIONS
            
        stream = CustomSynthesizeStream(tts=self, conn_options=conn_options)
        self._streams.add(stream)
        return stream
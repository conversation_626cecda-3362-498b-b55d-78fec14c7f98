from livekit.agents.tokenize import tokenizer, token_stream
from livekit.agents.tokenize.basic import SentenceTokenizer
import typing
import functools

def split_lines(
    text: str, min_sentence_len: int = 20, retain_format: bool = False
) -> list[str]:
    # Split by newline and filter out empty strings
    return [line for line in text.split('\n') if line.strip()]


class CustomBufferedTokenStream(token_stream.BufferedTokenStream):
    
    @typing.no_type_check
    def push_text(self, text: str) -> None:
        self._check_not_closed()
        self._in_buf += text

        if len(self._in_buf) < self._min_ctx_len:
            return

        while True:
            tokens = self._tokenize_fnc(self._in_buf)

            if len(tokens) <= 0:
                break

            if self._out_buf:
                self._out_buf += " "

            tok = tokens.pop(0)
            tok_text = tok
            if isinstance(tok, tuple):
                tok_text = tok[0]

            self._out_buf += tok_text
            if len(self._out_buf) >= self._min_token_len:
                self._event_ch.send_nowait(
                    tokenizer.TokenData(token=self._out_buf, segment_id=self._current_segment_id)
                )

                self._out_buf = ""

            if isinstance(tok, tuple):
                self._in_buf = self._in_buf[tok[2] :]
            else:
                tok_i = max(self._in_buf.find(tok), 0)
                self._in_buf = self._in_buf[tok_i + len(tok) :].lstrip()

class CustomBufferedSentenceStream(CustomBufferedTokenStream, tokenizer.SentenceStream):
    def __init__(
        self,
        *,
        tokenizer: token_stream.TokenizeCallable,
        min_token_len: int,
        min_ctx_len: int,
    ) -> None:
        super().__init__(
            tokenize_fnc=tokenizer,
            min_token_len=min_token_len,
            min_ctx_len=min_ctx_len,
        )

class CustomSentenceTokenizer(SentenceTokenizer):

    def stream(self, *, language: str | None = None) -> tokenizer.SentenceStream:
        return CustomBufferedSentenceStream(
            tokenizer=functools.partial(
                split_lines,
                min_sentence_len=self._config.min_sentence_len,
                retain_format=self._config.retain_format,
            ),
            min_token_len=self._config.min_sentence_len,
            min_ctx_len=self._config.stream_context_len,
        )
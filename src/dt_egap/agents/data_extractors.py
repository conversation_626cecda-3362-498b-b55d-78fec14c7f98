"""Optimized data extraction using single comprehensive prompts - IMPROVED VERSION"""

import json
import boto3
from datetime import date, datetime
from typing import Dict, Any, Optional, List, Literal
from dataclasses import dataclass
import logging

import os
os.environ['AWS_PROFILE'] = os.getenv('AWS_PROFILE', 'default')


@dataclass
class ExtractedData:
    """Complete extracted data from user input - IMPROVED to match models"""
    # Common fields
    confidence_score: float
    extraction_type: str  # "fertilizer", "chemical", "harvest", "general"
    
    # Fertilizer fields
    fertilizer_type: Optional[str] = None  # "organic", "chemical", "plant_growth_regulator", "other"
    fertilizer_name: Optional[str] = None
    fertilizer_quantity_kg_per_rai: Optional[float] = None
    fertilizer_purchase_date: Optional[date] = None
    fertilizer_application_date: Optional[date] = None
    fertilizer_applied_by: Optional[str] = None
    fertilizer_source_of_purchase: Optional[str] = None  # Added to match models
    
    # Chemical fields  
    chemical_name: Optional[str] = None
    chemical_common_name: Optional[str] = None  # Separate trade vs common name
    chemical_supplier: Optional[str] = None
    chemical_application_rate: Optional[float] = None
    chemical_application_date: Optional[date] = None
    chemical_applied_by: Optional[str] = None
    chemical_registration_number: Optional[str] = None  # Added
    chemical_mfg_exp_date: Optional[str] = None  # Added
    chemical_total_volume: Optional[float] = None  # Added
    pest_type: Optional[str] = None  # Added - missing field
    
    # Harvest fields
    crop_type: Optional[str] = None
    harvest_quantity_kg: Optional[float] = None
    harvest_date: Optional[date] = None
    harvested_by: Optional[Literal["self", "buyer", "hired_labor"]] = None  # Fixed type
    buyer_name: Optional[str] = None
    transport_vehicle: Optional[str] = None
    selling_price_per_kg: Optional[float] = None
    
    # General fields
    person_names: List[str] = None
    dates_mentioned: List[date] = None
    quantities_mentioned: List[float] = None
    
    def __post_init__(self):
        if self.person_names is None:
            self.person_names = []
        if self.dates_mentioned is None:
            self.dates_mentioned = []
        if self.quantities_mentioned is None:
            self.quantities_mentioned = []


class DataExtractor:
    """Single-prompt data extractor for all farm record types - IMPROVED"""
    
    def __init__(self, model_id: str = "apac.anthropic.claude-sonnet-4-20250514-v1:0"):
        self.bedrock = boto3.client('bedrock-runtime', region_name='ap-southeast-1')
        self.model_id = model_id
        self.logger = logging.getLogger(__name__)
    
    async def extract_all_farm_data(self, user_input: str) -> ExtractedData:
        """Extract all possible farm data in one comprehensive call - IMPROVED"""
        
        today = date.today()
        yesterday = date(today.year, today.month, today.day - 1) if today.day > 1 else today
        
        prompt = f"""Extract structured farm data from this Thai farmer's message: "{user_input}"

Today's date is {today.strftime('%Y-%m-%d')} for reference.

Please analyze and extract ALL relevant information into this JSON structure. Return ONLY the JSON, no other text.

{{
  "confidence_score": 0.0-1.0,
  "extraction_type": "fertilizer|chemical|harvest|general",
  
  // FERTILIZER DATA (if mentioned)
  "fertilizer_type": "organic|chemical|plant growth regulator|other",
  "fertilizer_name": "compost|organic fertilizer|chemical fertilizer|NPK|urea|null",
  "fertilizer_quantity_kg_per_rai": numeric_value_or_null,
  "fertilizer_purchase_date": "YYYY-MM-DD or null",
  "fertilizer_application_date": "YYYY-MM-DD or null", 
  "fertilizer_applied_by": "person_name or null",
  "fertilizer_source_of_purchase": "store_name_or_company or null",
  
  // CHEMICAL DATA (if mentioned) - IMPROVED with all required fields
  "chemical_name": "trade_name like GA3|Roundup|pesticide_brand or null",
  "chemical_common_name": "common_name like Gibberellic acid|Glyphosate or null",
  "chemical_supplier": "company_name or null",
  "chemical_application_rate": "numeric_rate_per_20L_water_or_per_rai or null",
  "chemical_application_date": "YYYY-MM-DD or null",
  "chemical_applied_by": "person_name or null",
  "chemical_registration_number": "registration_code or null",
  "chemical_mfg_exp_date": "manufacturing_expiry_info or null",
  "chemical_total_volume": "total_volume_used_in_liters or null",
  "pest_type": "insects|fungus|weeds|diseaseßs|pest_description or null",
  
  // HARVEST DATA (if mentioned)  
  "crop_type": "rice|vegetables|fruits|corn|cassava|crop_name or null",
  "harvest_quantity_kg": numeric_kg_value_or_null,
  "harvest_date": "YYYY-MM-DD or null",
  "harvested_by": "self|buyer|hired_labor",  // ONLY these 3 values allowed
  "buyer_name": "buyer_company_or_person or null",
  "transport_vehicle": "truck|pickup|motorcycle|cart or null", 
  "selling_price_per_kg": numeric_price_or_null,
  
  // EXTRACTED ENTITIES
  "person_names": ["list", "of", "names"],
  "dates_mentioned": ["YYYY-MM-DD", "list"],
  "quantities_mentioned": [numeric, "values"]
}}

IMPROVED EXTRACTION RULES:
1. Convert relative dates: "yesterday" = {yesterday.strftime('%Y-%m-%d')}, "today" = {today.strftime('%Y-%m-%d')}, "last week" = estimate
2. Extract ALL numeric quantities with their context and units
3. Standardize fertilizer names: "ปุ่ยหมัก" = "compost", "ปุ่ยเคมี" = "chemical fertilizer", "ปุ่ยยูเรีย" = "urea"
4. For chemicals: distinguish trade name vs common name ("จีเอ3" trade_name = "GA3", common_name = "Gibberellic acid")
5. IMPORTANT: harvested_by can ONLY be "self", "buyer", or "hired_labor" - never "family" or other values
6. Extract pest information: "แมลง" = "insects", "เชื้อรา" = "fungus", "วัชพืช" = "weeds"
7. Set confidence based on completeness and clarity of information
8. Set extraction_type to the PRIMARY activity mentioned
9. If multiple activities mentioned, choose the one with most complete information

VALIDATION RULES:
- harvested_by: Must be exactly "self", "buyer", or "hired_labor"
- All numeric values must be positive
- Dates must be valid and not in future (except today)
- If confidence < 0.5, mark as "general" type

EXAMPLES:
Input: "เมื่อวานใช้ปุ่ยหมัก 15 กิโล ต่อไร่ ซื้อจากร้านเกษตร"
Output: {{"confidence_score": 0.9, "extraction_type": "fertilizer", "fertilizer_name": "compost", "fertilizer_quantity_kg_per_rai": 15, "fertilizer_application_date": "{yesterday.strftime('%Y-%m-%d')}", "fertilizer_source_of_purchase": "ร้านเกษตร", ...}}

Input: "เก็บผลผลิตข้าว 200 กิโล วันนี้ ตัวเองเก็บ"
Output: {{"confidence_score": 0.95, "extraction_type": "harvest", "crop_type": "rice", "harvest_quantity_kg": 200, "harvest_date": "{today.strftime('%Y-%m-%d')}", "harvested_by": "self", ...}}

Input: "ฉีดยาฆ่าแมลง GA3 อัตรา 2 มล. ต่อน้ำ 20 ลิตร"
Output: {{"confidence_score": 0.9, "extraction_type": "chemical", "chemical_name": "GA3", "chemical_common_name": "Gibberellic acid", "chemical_application_rate": 2, "pest_type": "insects", ...}}

Example of correct response format:
{{
  "confidence_score": 0.8,
  "extraction_type": "fertilizer"
}}

Example of INCORRECT response format:
```json
{{
  "confidence_score": 0.8
}}
"""

        print(f"Extracting data with prompt:\n{prompt}\n")

        try:
            response = self.bedrock.invoke_model(
                modelId=self.model_id,
                body=json.dumps({
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 1500,
                    "temperature": 0.1,  # Low temperature for consistent extraction
                    "messages": [{"role": "user", "content": prompt}]
                }),
                contentType="application/json"
            )
            
            result = json.loads(response['body'].read())
            claude_response = result['content'][0]['text'].strip()

            print(f"Claude response:\n{claude_response}\n")
            
            # Parse the JSON response
            try:
                extracted_json = json.loads(claude_response)
                return self._json_to_extracted_data(extracted_json)
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse Claude JSON response: {e}")
                return ExtractedData(confidence_score=0.0, extraction_type="general")
                
        except Exception as e:
            self.logger.error(f"Error in unified extraction: {e}")
            return ExtractedData(confidence_score=0.0, extraction_type="general")
    
    def _json_to_extracted_data(self, json_data: Dict[str, Any]) -> ExtractedData:
        """Convert JSON response to ExtractedData object with validation"""
        
        def parse_date(date_str: str) -> Optional[date]:
            """Parse date string to date object"""
            if not date_str or date_str == "null":
                return None
            try:
                parsed_date = datetime.fromisoformat(date_str).date()
                # Don't allow future dates (except today)
                if parsed_date > date.today():
                    return None
                return parsed_date
            except (ValueError, TypeError):
                return None
        
        def safe_get(key: str, convert_func=None, validator=None):
            """Safely get value from JSON with optional conversion and validation"""
            value = json_data.get(key)
            if value is None or value == "null":
                return None
            
            if convert_func:
                try:
                    value = convert_func(value)
                except:
                    return None
            
            if validator and not validator(value):
                return None
                
            return value
        
        def validate_harvested_by(value: str) -> bool:
            """Validate harvested_by field"""
            return value in ["self", "buyer", "hired_labor"] if value else True
        
        def validate_positive_number(value: float) -> bool:
            """Validate positive numeric values"""
            return value > 0 if value is not None else True
        
        return ExtractedData(
            confidence_score=safe_get("confidence_score", float, lambda x: 0 <= x <= 1) or 0.0,
            extraction_type=safe_get("extraction_type", str) or "general",
            
            # Fertilizer fields - with validation
            fertilizer_type=safe_get("fertilizer_type", str),
            fertilizer_name=safe_get("fertilizer_name", str),
            fertilizer_quantity_kg_per_rai=safe_get("fertilizer_quantity_kg_per_rai", float, validate_positive_number),
            fertilizer_purchase_date=parse_date(safe_get("fertilizer_purchase_date", str)),
            fertilizer_application_date=parse_date(safe_get("fertilizer_application_date", str)),
            fertilizer_applied_by=safe_get("fertilizer_applied_by", str),
            fertilizer_source_of_purchase=safe_get("fertilizer_source_of_purchase", str),
            
            # Chemical fields - improved
            chemical_name=safe_get("chemical_name", str),
            chemical_common_name=safe_get("chemical_common_name", str),
            chemical_supplier=safe_get("chemical_supplier", str),
            chemical_application_rate=safe_get("chemical_application_rate", float, validate_positive_number),
            chemical_application_date=parse_date(safe_get("chemical_application_date", str)),
            chemical_applied_by=safe_get("chemical_applied_by", str),
            chemical_registration_number=safe_get("chemical_registration_number", str),
            chemical_mfg_exp_date=safe_get("chemical_mfg_exp_date", str),
            chemical_total_volume=safe_get("chemical_total_volume", float, validate_positive_number),
            pest_type=safe_get("pest_type", str),
            
            # Harvest fields - with strict validation
            crop_type=safe_get("crop_type", str),
            harvest_quantity_kg=safe_get("harvest_quantity_kg", float, validate_positive_number),
            harvest_date=parse_date(safe_get("harvest_date", str)),
            harvested_by=safe_get("harvested_by", str, validate_harvested_by),
            buyer_name=safe_get("buyer_name", str),
            transport_vehicle=safe_get("transport_vehicle", str),
            selling_price_per_kg=safe_get("selling_price_per_kg", float, validate_positive_number),
            
            # General fields
            person_names=safe_get("person_names", list) or [],
            dates_mentioned=[parse_date(d) for d in (safe_get("dates_mentioned", list) or []) if parse_date(d)],
            quantities_mentioned=[q for q in (safe_get("quantities_mentioned", list) or []) if isinstance(q, (int, float)) and q > 0]
        )
    
    def validate_extracted_data(self, data: ExtractedData) -> ExtractedData:
        """Additional validation and confidence adjustment"""
        
        # Reduce confidence if required fields are missing for the extracted type
        if data.extraction_type == "fertilizer":
            if not data.fertilizer_name or not data.fertilizer_quantity_kg_per_rai:
                data.confidence_score *= 0.7
                
        elif data.extraction_type == "chemical":
            if not data.chemical_name:
                data.confidence_score *= 0.7
                
        elif data.extraction_type == "harvest":
            if not data.crop_type or not data.harvest_quantity_kg:
                data.confidence_score *= 0.7
        
        # If confidence becomes too low, change to general
        if data.confidence_score < 0.5:
            data.extraction_type = "general"
        
        return data
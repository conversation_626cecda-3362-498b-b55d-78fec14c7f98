from livekit.agents import Agent, function_tool, RunContext, get_job_context
from livekit.plugins import aws
import logging
import json
from datetime import date, datetime
from typing import Optional, Dict, Any, List

# Import your existing models
from ..models import FarmRecords, FertilizerRecord, ChemicalRecord, HarvestRecord, FertilizerType
from .data_extractors import ExtractedData, DataExtractor


class SchemaFillingAgent(Agent):
    """e-GAP farm record collection assistant using LiveKit Agents"""
    
    def __init__(self) -> None:
        super().__init__(
            instructions="""You are an e-GAP farm record collection assistant. Your job is to help farmers record their agricultural activities through natural conversations.

Key guidelines:
1. Always greet farmers warmly and ask what they'd like to record today
2. For simple greetings or casual conversation, respond naturally WITHOUT using any function tools
3. Ask ONE focused follow-up question at a time for missing critical information
4. Use natural, conversational language - don't sound robotic
5. Acknowledge previous records with ordinal counts (first, second, third time...)
6. Only use function tools AFTER you have gathered sufficient information through conversation
7. Follow the conversation flow patterns from the script scenarios

Activity Classification (set extraction_type correctly):
- FERTILIZER: compost, manure, organic fertilizer, chemical fertilizer, NPK, urea, etc.
- CHEMICAL: pesticides, herbicides, fungicides, GA3, gibberellic acid, agricultural chemicals
- HARVEST: harvesting crops, picking fruits, collecting produce, crop yields

Conversation patterns:
- For fertilizer: Ask about purchase date, quantity, and who applied it
- For chemicals: Ask about who will apply and when
- For harvest: Ask if they harvested it themselves, then gather buyer/sales info if needed
- Always acknowledge the recording count for each activity type

Remember: Have a natural conversation FIRST, if user provides any relevant information, call the function tools."""
        )
        
        # Initialize data models
        self.farm_records = FarmRecords()
        self.unified_extractor = DataExtractor()
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        # self.session_id = 
        
        # Conversation state tracking
        self.conversation_state = {
            "current_activity": None,
            "pending_data": {},
            "missing_fields": [],
            "awaiting_input": False
        }

    def gen_session_id(self) -> str:
        """Generate a random session ID for this agent instance"""
        import uuid
        return str(uuid.uuid4())

    async def on_enter(self) -> None:
        """Called when agent starts - send greeting message"""
        await self.session.generate_reply(
            instructions="Greet the farmer warmly and ask 'What would you like to record today?' Show suggestion cards for: Fertilizer usage, Agricultural chemical usage, Harvesting"
        )

    @function_tool()
    async def handle_user_message(
        self, 
        context: RunContext,
        user_message: str
    ) -> str:
        """Main function to handle user messages with natural conversation flow, always call this first"""
        
        session_info = self._get_session_info(context)

        print(f"Session info: {json.dumps(session_info, indent=2)}")

        print(f"Session info, {context.session}")

        print(f"Chat context: {self.chat_ctx.items}")
        
        try:
            print(f"User message: {user_message}")
            
            # Extract information from user message
            extracted = await self.unified_extractor.extract_all_farm_data(user_message)
            print(f"Extracted data: {extracted}")
            
            # Determine activity type if not already set
            if not self.conversation_state["current_activity"]:
                activity_type = self._detect_activity_type(extracted, user_message)
                if activity_type:
                    self.conversation_state["current_activity"] = activity_type
                    self.conversation_state["pending_data"] = self._extract_to_dict(extracted)
                else:
                    return "I'm not sure what you'd like to record. Could you tell me if you want to record fertilizer usage, agricultural chemical usage, or harvesting?"
            
            # Update pending data with new information
            new_data = self._extract_to_dict(extracted)
            self.conversation_state["pending_data"].update({k: v for k, v in new_data.items() if v})
            
            # Handle based on current activity
            if self.conversation_state["current_activity"] == "fertilizer":
                return await self._handle_fertilizer_conversation()
            elif self.conversation_state["current_activity"] == "chemical":
                return await self._handle_chemical_conversation()
            elif self.conversation_state["current_activity"] == "harvest":
                return await self._handle_harvest_conversation()
            
            return "I'm having trouble understanding. Could you provide more details?"
            
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            return "I had trouble understanding that. Could you tell me more about what you'd like to record?"
    
    def _detect_activity_type(self, extracted: ExtractedData, user_message: str) -> Optional[str]:
        """Detect what type of activity the user wants to record using AI extraction"""
        # Use the AI extractor's classification
        if extracted.extraction_type == "fertilizer" and extracted.confidence_score > 0.5:
            return "fertilizer"
        elif extracted.extraction_type == "chemical" and extracted.confidence_score > 0.5:
            return "chemical"
        elif extracted.extraction_type == "harvest" and extracted.confidence_score > 0.5:
            return "harvest"
        
        # Fallback: check if we have specific data fields populated
        if extracted.fertilizer_name or extracted.fertilizer_quantity_kg_per_rai:
            return "fertilizer"
        elif extracted.chemical_name or extracted.chemical_supplier:
            return "chemical"
        elif extracted.crop_type or extracted.harvest_quantity_kg:
            return "harvest"
        
        return None
    
    def _extract_to_dict(self, extracted: ExtractedData) -> Dict[str, Any]:
        """Convert extracted data to dictionary for easier handling"""
        return {
            "fertilizer_name": extracted.fertilizer_name,
            "fertilizer_quantity": extracted.fertilizer_quantity_kg_per_rai,
            "fertilizer_purchase_date": extracted.fertilizer_purchase_date,
            "fertilizer_applied_by": extracted.fertilizer_applied_by,
            "fertilizer_application_date": extracted.fertilizer_application_date,
            "chemical_name": extracted.chemical_name,
            "chemical_supplier": extracted.chemical_supplier,
            "chemical_application_rate": extracted.chemical_application_rate,
            "chemical_applied_by": extracted.chemical_applied_by,
            "chemical_application_date": extracted.chemical_application_date,
            "crop_type": extracted.crop_type,
            "harvest_quantity": extracted.harvest_quantity_kg,
            "harvest_date": extracted.harvest_date,
            "harvested_by": extracted.harvested_by,
            "buyer_name": extracted.buyer_name,
            "transport_vehicle": extracted.transport_vehicle,
            "selling_price": extracted.selling_price_per_kg
        }
    
    async def _handle_fertilizer_conversation(self) -> str:
        """Handle fertilizer recording conversation following script pattern"""
        data = self.conversation_state["pending_data"]
        
        # Check what information we still need
        missing = []
        if not data.get("fertilizer_name"):
            missing.append("fertilizer_name")
        if not data.get("fertilizer_purchase_date"):
            missing.append("purchase_date")
        if not data.get("fertilizer_quantity"):
            missing.append("quantity")
        if not data.get("fertilizer_applied_by"):
            missing.append("applicator")
        
        # Ask for missing information following script pattern
        if "purchase_date" in missing or "quantity" in missing or "applicator" in missing:
            questions = []
            if "purchase_date" in missing:
                questions.append("When did you buy it?")
            if "quantity" in missing:
                questions.append("How much did you use?")
            if "applicator" in missing:
                questions.append("And who applied it?")
            
            return " ".join(questions)
        
        # All information collected - save the record
        return await self._save_fertilizer_record()
    
    async def _handle_chemical_conversation(self) -> str:
        """Handle chemical recording conversation following script pattern"""
        data = self.conversation_state["pending_data"]
        
        # Check what information we still need
        if not data.get("chemical_applied_by") or not data.get("chemical_application_date"):
            questions = []
            if not data.get("chemical_applied_by"):
                questions.append("Who will apply it?")
            if not data.get("chemical_application_date"):
                questions.append("And when will he apply it?")
            
            return " ".join(questions)
        
        # All information collected - save the record
        return await self._save_chemical_record()
    
    async def _handle_harvest_conversation(self) -> str:
        """Handle harvest recording conversation following script pattern"""
        data = self.conversation_state["pending_data"]
        
        # First check if we know who harvested
        if not data.get("harvested_by"):
            return "Did you harvest it yourself?"
        
        # If buyer harvested, we might need buyer details
        if data.get("harvested_by") in ["buyer", "no"]:
            # Check harvest count to determine if we need sales info
            harvest_count = self.farm_records.get_crop_harvest_count(data.get("crop_type", ""))
            
            if harvest_count >= 2:  # Third harvest or more - ask for sales info
                if not data.get("buyer_name") or not data.get("transport_vehicle"):
                    return "Who is the buyer? What vehicle was used for transport?"
                elif not data.get("selling_price"):
                    return "What is the Selling Price (THB per fruit/kg)?"
        
        # All information collected - save the record
        return await self._save_harvest_record()
    
    async def _save_fertilizer_record(self) -> str:
        """Save fertilizer record and provide acknowledgment"""
        data = self.conversation_state["pending_data"]
        
        # # Get usage count before adding new record
        # usage_count = self.farm_records.get_all_fertilizer_records_count(data["fertilizer_name"])
        usage_count = 0
        # Create and save record
        record = FertilizerRecord(
            fertilizer_type=FertilizerType.ORGANIC if "compost" in data["fertilizer_name"].lower() or "manure" in data["fertilizer_name"].lower() else FertilizerType.OTHER,
            type_formula_name=data["fertilizer_name"],
            purchase_date=data.get("fertilizer_purchase_date") or date.today(),
            source_of_purchase="Not specified",
            rate_of_use=data["fertilizer_quantity"],
            usage_date=data.get("fertilizer_application_date") or date.today(),
            operator_name=data["fertilizer_applied_by"]
        )
        
        self.farm_records.add_fertilizer_record(record)
        
        # Clear conversation state
        self._reset_conversation_state()
        
        # Return acknowledgment following script pattern
        ordinal = self._get_ordinal(usage_count + 1)
        return f"(Just so you know) — this is your {ordinal} time recording {data['fertilizer_name']} usage. Would you like to record anything else?"
    
    async def _save_chemical_record(self) -> str:
        """Save chemical record and provide acknowledgment"""
        data = self.conversation_state["pending_data"]
        
        # Get usage count before adding new record
        # usage_count = self.farm_records.get_all_chemical_records_count(data["chemical_name"])
        usage_count = 0

        # Create and save record
        record = ChemicalRecord(
            trade_name=data["chemical_name"],
            common_name=data["chemical_name"],
            registration_of_hazardous_substances="Not specified",
            mfg_exp_date="Not specified",
            purchase_date=date.today(),
            source_of_purchase=data.get("chemical_supplier", "Not specified"),
            application_date=data.get("chemical_application_date") or date.today(),
            rate_of_use=data.get("chemical_application_rate", 0.0),
            total_volume=0.0,
            worker_name=data["chemical_applied_by"]
        )
        
        self.farm_records.add_chemical_record(record)
        
        # Clear conversation state
        self._reset_conversation_state()
        
        # Return acknowledgment following script pattern
        ordinal = self._get_ordinal(usage_count + 1)
        return f"This is your {ordinal} time recording {data['chemical_name']} usage. I've saved the details to your e-GAP log."
    
    async def _save_harvest_record(self) -> str:
        """Save harvest record and provide acknowledgment"""
        data = self.conversation_state["pending_data"]
        
        # Get harvest count before adding new record
        harvest_count = self.farm_records.get_crop_harvest_count(data["crop_type"])
        total_harvest = self.farm_records.get_total_crop_harvest(data["crop_type"])
        
        # Create and save record
        record = HarvestRecord(
            crop_type=data["crop_type"],
            quantity_kg=data["harvest_quantity"],
            harvest_date=data.get("harvest_date") or date.today(),
            harvested_by=data.get("harvested_by", "self"),
            buyer_name=data.get("buyer_name"),
            transport_vehicle=data.get("transport_vehicle"),
            selling_price_per_kg=data.get("selling_price")
        )
        
        self.farm_records.add_harvest_record(record)
        
        # Clear conversation state
        self._reset_conversation_state()
        
        # Return acknowledgment following script pattern
        ordinal = self._get_ordinal(harvest_count + 1)
        new_total = total_harvest + data["harvest_quantity"]
        
        if harvest_count >= 2:  # Third harvest or more
            return f"Thanks — this is your {ordinal} harvest. So far, your total {data['crop_type']} production is {new_total} kg."
        else:
            return f"{'Okay, noted' if harvest_count == 0 else 'Got it'} — this is your {ordinal} {data['crop_type']} harvest."
    
    def _get_ordinal(self, n: int) -> str:
        """Convert number to ordinal word"""
        ordinals = {1: "first", 2: "second", 3: "third", 4: "fourth", 5: "fifth"}
        return ordinals.get(n, f"{n}th")
    
    def _reset_conversation_state(self):
        """Reset conversation state after completing a record"""
        self.conversation_state = {
            "current_activity": None,
            "pending_data": {},
            "missing_fields": [],
            "awaiting_input": False
        }
    
    @function_tool()
    async def check_record_completion (self, context: RunContext) -> str:
        """Check if user has enough records for e-GAP form completion"""
        total_records = (len(self.farm_records.fertilizer_records) + 
                        len(self.farm_records.chemical_records) + 
                        len(self.farm_records.harvest_records))
        
        if total_records >= 5:  # Arbitrary threshold
            return "Your data is complete and ready for e-GAP submission. Would you like to review your e-GAP form?"
        
        return f"You have recorded {total_records} activities so far. Continue recording to complete your e-GAP form."

    
    def _get_session_info(self, context: RunContext) -> Dict[str, Any]:
        """Extract basic session information from RunContext"""
        session_info = {}
        
        try:
            # Get room through job context (recommended way)
            room = get_job_context().room
            session_info['room_sid'] = room.sid
            session_info['room_name'] = room.name
            session_info['room_metadata'] = room.metadata
            
            # Get participant info
            participants = []
            for participant in room.remote_participants.values():
                participants.append({
                    'sid': participant.sid,
                    'identity': participant.identity,
                    'name': participant.name
                })
            session_info['participants'] = participants
            
            # Add timestamp
            session_info['timestamp'] = datetime.now().isoformat()
                
        except Exception as e:
            session_info['error'] = str(e)
            
        return session_info

# Entry point function
async def entrypoint(ctx):
    """Entry point for the farm record agent"""
    from livekit.agents import AgentSession
    
    logger = logging.getLogger("farm-agent")
    logger.info("Starting e-GAP farm record agent")
    
    await ctx.connect()
    
    agent = SchemaFillingAgent()
    
    session = AgentSession(
        # Pass LLM to session
        llm=aws.LLM(model="anthropic.claude-3-5-sonnet-20240620-v1:0"),
        
        # Configure for voice input/output
        # You can change these based on whether you want voice or text only
    )
    
    await session.start(agent=agent, room=ctx.room)
    logger.info("e-GAP farm record agent is ready!")


if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
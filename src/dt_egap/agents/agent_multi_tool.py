from livekit.agents import Agent, function_tool, RunContext
from livekit.plugins import aws
import logging
import json
from datetime import date, datetime
from typing import Optional, Dict, Any, List
from livekit.agents import stt

from dotenv import load_dotenv
import os
load_dotenv()

# Import your existing models
from ..models import *
from .record_handlers import RecordHandlers


class MainFarmAgent(Agent):
    """Main agent that coordinates farm record activities with conversation flow"""
    
    def __init__(self):
        today = date.today()
        yesterday = date(today.year, today.month, today.day - 1) if today.day > 1 else today
        debug_in_eng = os.getenv("DEBUG_IN_ENG", "false").lower() == "true"

        instructions=f"""
            You are the main e-GAP farm record collection assistant. Your role is to:

            1. Greet farmers warmly and understand what they want to record
            2. For simple greetings or casual conversation, respond shortly WITHOUT using any function tools
            3. Use the appropriate function tools when farmers mention specific activities:
            - Use record_fertilizer_usage for fertilizer applications
            - Use record_harvesting for harvest records
            4. Let each tool handle its own conversation flow and data collection
            5. Provide general guidance and help farmers navigate the system
            6. Offer summaries of recorded activities when requested
            7. Convert relative dates: "yesterday" = {yesterday.strftime('%Y-%m-%d')}, "today" = {today.strftime('%Y-%m-%d')}, "last week" = estimate
            8. If you have information about the first or second time a fertilizer was used, use ordinal numbers like "first", "second", "third" in your responses

            **Fertilizer Classification Reference**:
            1. Chemical Fertilizers:

            - Urea (46-0-0), Ammonium nitrate (34-0-0), Calcium ammonium nitrate, Calcium nitrate
            - Mono-potassium phosphate (MKP), Di-ammonium phosphate (DAP)
            - Potassium sulfate (K2SO4), Potassium nitrate (KNO3)
            - NPK 15-15-15, NPK 0-30-20 or 10-60-10

            2. Organic Fertilizers:
            - Chicken manure, Cow manure, Composted manure (livestock), Green manure
            - Humic acid, Seaweed extract (kelp), Amino acid-based liquid
            - Bacillus subtilis (biofertilizer), Vinasse (molasses residue), Silkworm excrement

            3. Plant Growth Regulators:

            - Paclobutrazol, Uniconazole, Dikegulac sodium
            - Gibberellic acid (GA3), Naphthaleneacetic acid (NAA), Salicylic acid (SA)
            - Seaweed extract, Humic acid (biostimulant), Amino acid fertilizers, Chelated iron (EDDHA-Fe)

            **Activity Classification:**

            - FERTILIZER: All items listed above in fertilizer categories
            - HARVEST: harvesting crops, picking fruits, collecting produce, crop yields

            Today's date is {today.strftime('%Y-%m-%d')} for reference.
            Be friendly, efficient, and help farmers get their agricultural activities documented properly.
            Each function tool will handle its own conversation flow and only save records when complete information is collected.
            
            **NOTE**: In greetings, respond shortly with only 3 sentences, like "Hello, I am your e-GAP farm record assistant. What agricultural activity would you like to record today? I can help with fertilizer or harvest records."
        """
        if debug_in_eng:
            instructions += "\nPlease answer with English."

        super().__init__(
            instructions=instructions
        )
        
        # Initialize shared data model
        self.farm_records = FarmRecords()
        self.logger = logging.getLogger(f"{__name__}.MainFarmAgent")
        
        # # Conversation state tracking (needed for multi-step conversations in tools)
        # self.conversation_state = {
        #     "current_activity": None,
        #     "pending_data": {},
        #     "missing_fields": [],
        #     "awaiting_input": False,
        #     "record_id": None,
        # }

        self.record_handlers = RecordHandlers(self.farm_records)

    async def on_enter(self) -> None:
        """Send greeting when agent starts"""
        await self.session.generate_reply(
            instructions="Say exactly: Hello I am your e-GAP farm record assistant. What agricultural activity would you like to record today?"
        )

    @function_tool(
        description = """"
    Record fertilizer application details when farmer mentions 
    using fertilizer, nutrients, or soil amendments. This tool handles the 
    conversation flow and only saves when all required information is collected.
    
    USE WHEN: Farmer mentions fertilizer names, application dates, quantities, 
    or any fertilizer-related activities."""
    )
    async def record_fertilizer_usage(
        self,
        args: FertilizerUsageSchema
    ) -> str:
        """Record fertilizer usage with conversation flow - only save when complete"""
        try:
            # Clear conversation state
            self.record_handlers._reset_conversation_state()
            # Set current activity
            self.record_handlers.current_activity = "fertilizer"
            
            # Update pending data with provided information
            self.record_handlers.update_pending_data(
                fertilizer_name=args.fertilizer_name,
                fertilizer_type=args.fertilizer_type,
                quantity_kg_per_rai=args.quantity_kg_per_rai,
                application_date=args.application_date.isoformat() if args.application_date else None,
                purchase_date=args.purchase_date.isoformat() if args.purchase_date else None,
                applied_by=args.applied_by,
                source_of_purchase=args.source_of_purchase
            )

            # Check what information we still need (following original logic)
            missing = []
            pending = self.record_handlers.pending_data

            print(f"Pending fertilizer data: {pending}")
            
            if not pending.get("fertilizer_name"):
                missing.append("fertilizer_name")
            if not pending.get("purchase_date"):
                missing.append("purchase_date")
            if not pending.get("quantity_kg_per_rai"):
                missing.append("quantity")
            if not pending.get("applied_by"):
                missing.append("applicator")
            if not pending.get("application_date"):
                missing.append("application_date")

            print(f"Missing fields: {missing}")
            
            # Ask for missing information following script pattern
            if missing:
                questions = []
                if "purchase_date" in missing:
                    questions.append("When did you buy it?")
                if "quantity" in missing:
                    questions.append("How much did you use (kg per rai)?")
                if "applicator" in missing:
                    questions.append("Who applied it?")
                if "application_date" in missing:
                    questions.append("When did you apply it?")
                
                return " ".join(questions) if questions else "Could you provide more details about the fertilizer?"
            
            # All information collected - save the record
            return await self.record_handlers._save_fertilizer_record()
            
        except Exception as e:
            self.logger.error(f"Error in fertilizer conversation: {e}")
            return "I had trouble understanding that fertilizer information. Could you tell me the fertilizer name and how much you used?"

    @function_tool(
        description = """Edit existing fertilizer usage records. This tool handles the 
        conversation flow and only saves when all required information is collected."""
    )
    async def edit_fertilizer_usage(
        self,
        args: FertilizerUsageSchema
    ) -> str:
        """Edit fertilizer usage with conversation flow - only save when complete"""

        if not self.record_handlers.record_id:
            return "I don't have a fertilizer record information to edit. Please record a new fertilizer usage."

        try:
            # Set current activity
            self.record_handlers.current_activity = "fertilizer"
            
            # Update pending data with provided information
           
            self.record_handlers.update_pending_data(
                fertilizer_name=args.fertilizer_name,
                fertilizer_type=args.fertilizer_type,
                quantity_kg_per_rai=args.quantity_kg_per_rai,
                application_date=args.application_date.isoformat() if args.application_date else None,
                purchase_date=args.purchase_date.isoformat() if args.purchase_date else None,
                applied_by=args.applied_by,
                source_of_purchase=args.source_of_purchase
            )

            # Check what information we still need (following original logic)
            missing = []
            pending = self.record_handlers.pending_data

            print(f"Pending fertilizer data: {pending}")
            
            if not pending.get("fertilizer_name"):
                missing.append("fertilizer_name")
            if not pending.get("purchase_date"):
                missing.append("purchase_date")
            if not pending.get("quantity_kg_per_rai"):
                missing.append("quantity")
            if not pending.get("applied_by"):
                missing.append("applicator")
            if not pending.get("application_date"):
                missing.append("application_date")

            print(f"Missing fields: {missing}")
            
            # Ask for missing information following script pattern
            if missing:
                questions = []
                if "purchase_date" in missing:
                    questions.append("When did you buy it?")
                if "quantity" in missing:
                    questions.append("How much did you use (kg per rai)?")
                if "applicator" in missing:
                    questions.append("Who applied it?")
                if "application_date" in missing:
                    questions.append("When did you apply it?")
                
                return " ".join(questions) if questions else "Could you provide more details about the fertilizer?"
            
            # All information collected - save the record
            return await self._edit_fertilizer_record()
            
        except Exception as e:
            self.logger.error(f"Error in fertilizer conversation: {e}")
            return "I had trouble understanding that fertilizer information. Could you tell me the fertilizer name and how much you used?"

    @function_tool(
    description="Record harvest activities when farmer mentions harvesting crops, selling produce, or completing crop cycles. Handles conversation flow and only saves when complete.",
    )
    async def record_harvesting(
        self,
        args: HarvestRecordingSchema
    ) -> str:
        """Record harvest with conversation flow - only save when complete"""
        
        try:
            self.record_handlers._reset_conversation_state()
            
            # Set current activity
            self.record_handlers.current_activity = "harvest"
            
            # Update pending data
            pending = self.record_handlers.pending_data
            self.record_handlers.update_pending_data(
                crop_type=args.crop_type,
                harvest_quantity=args.harvest_quantity,
                harvest_date=args.harvest_date.isoformat() if args.harvest_date else None,
                harvest_by=args.harvest_by,
                buyer_name=args.buyer_name,
                transport_vehicle=args.transport_vehicle,
                selling_price_per_kg=args.selling_price_per_kg
            )

            # First check if we know who harvested (following script pattern)
            if not pending.get("harvest_by"):
                return "Did you harvest it yourself?"
            
            # If buyer harvested, we might need buyer details
            if pending.get("harvest_by") in ["buyer", "no"]:
                # Check harvest count to determine if we need sales info
                harvest_count = self.farm_records.get_crop_harvest_count(pending.get("crop_type", ""))
                
                if harvest_count >= 2:  # Third harvest or more - ask for sales info
                    if not pending.get("buyer_name") or not pending.get("transport_vehicle"):
                        return "Who is the buyer? What vehicle was used for transport?"
                    elif not pending.get("selling_price_per_kg"):
                        return "What is the Selling Price (THB per fruit/kg)?"
            
            # Check if we have minimum required info
            if not pending.get("harvest_quantity"):
                return "How much did you harvest (in kg)?"
            
            # All information collected - save the record
            return await self.record_handlers._save_harvest_record()
                
        except Exception as e:
            self.logger.error(f"Error in harvest conversation: {e}")
            return "I had trouble understanding that harvest information. Could you tell me the crop type and quantity again?"


    @function_tool(
        raw_schema={
            "name": "get_farm_records_summary",
            "description": "Get a summary of all recorded farm activities",
            "parameters": {
                "type": "object",
                "properties": {
                    "include_details": {
                        "type": "boolean",
                        "description": "Whether to include detailed breakdown of each record type",
                        "default": False
                    }
                }
            }
        }
    )
    async def get_farm_records_summary(
        self,
        raw_arguments: Dict[str, Any]
    ) -> str:
        """Provide a summary of all recorded farm activities"""
        try:
            include_details = raw_arguments.get("include_details", False)
            
            summary_parts = []
            
            # Fertilizer summary
            fertilizer_count = len(self.farm_records.fertilizer_records)
            if fertilizer_count > 0:
                summary_parts.append(f"🌱 {fertilizer_count} fertilizer application{'s' if fertilizer_count > 1 else ''}")
            
            # Chemical summary  
            chemical_count = len(self.farm_records.chemical_records)
            if chemical_count > 0:
                summary_parts.append(f"💧 {chemical_count} chemical application{'s' if chemical_count > 1 else ''}")
            
            # Harvest summary
            harvest_count = len(self.farm_records.harvest_records)
            if harvest_count > 0:
                summary_parts.append(f"🌾 {harvest_count} harvest record{'s' if harvest_count > 1 else ''}")
            
            if not summary_parts:
                return "You haven't recorded any farm activities yet. What would you like to record today?"
            
            base_summary = "Here's your farm record summary:\n" + "\n".join(summary_parts)
            
            if include_details:
                # Add more detailed breakdown if requested
                details = []
                if fertilizer_count > 0:
                    fert_types = set(record.type_formula_name for record in self.farm_records.fertilizer_records)
                    details.append(f"Fertilizers used: {', '.join(fert_types)}")
                
                if chemical_count > 0:
                    chem_types = set(record.trade_name for record in self.farm_records.chemical_records)
                    details.append(f"Chemicals applied: {', '.join(chem_types)}")
                
                if harvest_count > 0:
                    crop_types = set(record.crop_type for record in self.farm_records.harvest_records)
                    details.append(f"Crops harvested: {', '.join(crop_types)}")
                
                if details:
                    base_summary += "\n\nDetails:\n" + "\n".join(details)
            
            return base_summary + "\n\nWould you like to add more records or need help with e-GAP compliance?"
            
        except Exception as e:
            self.logger.error(f"Error generating summary: {e}")
            return "I had trouble accessing your records. Would you like to record a new farming activity?"

    @function_tool()
    async def check_record_completion(self, context: RunContext) -> str:
        """Check if user has enough records for e-GAP form completion"""
        total_records = (len(self.farm_records.fertilizer_records) + 
                        len(self.farm_records.chemical_records) + 
                        len(self.farm_records.harvest_records))
        
        if total_records >= 5:  # Arbitrary threshold
            return "Your data is complete and ready for e-GAP submission. Would you like to review your e-GAP form?"
        
        return f"You have recorded {total_records} activities so far. Continue recording to complete your e-GAP form."

    def _get_ordinal(self, n: int) -> str:
        """Convert number to ordinal word"""
        ordinals = {1: "first", 2: "second", 3: "third", 4: "fourth", 5: "fifth"}
        return ordinals.get(n, f"{n}th")

    def stt_node(self, audio, model_settings):
        """Override STT node để log kết quả"""
        
        # Gọi STT node mặc định
        stt_stream = super().stt_node(audio, model_settings)
        
        # Wrap stream để log events
        return self._wrap_stt_stream(stt_stream)
    
    async def _wrap_stt_stream(self, stt_stream):
        """Wrap STT stream để log kết quả"""
        async for speech_event in stt_stream:
            # Log theo loại event
            if speech_event.type == stt.SpeechEventType.INTERIM_TRANSCRIPT:
                if speech_event.alternatives:
                    text = speech_event.alternatives[0].text
                    print(f"[STT INTERIM] {text}")
            
            elif speech_event.type == stt.SpeechEventType.FINAL_TRANSCRIPT:
                if speech_event.alternatives:
                    text = speech_event.alternatives[0].text
                    print(f"[STT FINAL] {text}")
                    logging.info(f"STT Final: {text}")
            
            elif speech_event.type == stt.SpeechEventType.START_OF_SPEECH:
                print("[STT] Speech started")
            
            # Yield event để tiếp tục pipeline
            yield speech_event



# Entry point function
async def entrypoint(ctx):
    """Entry point for the enhanced single agent farm record system"""
    from livekit.agents import AgentSession
    
    logger = logging.getLogger("enhanced-farm-agent")
    logger.info("Starting enhanced e-GAP farm record agent with conversation flow")
    
    await ctx.connect()
    
    # Create main agent
    main_agent = MainFarmAgent()
    
    session = AgentSession(
        # Configure LLM
        llm=aws.LLM(model="anthropic.claude-3-5-sonnet-20240620-v1:0"),
        
        # Configure for voice/text based on your needs
        # You can add voice configuration here if needed
    )
    
    await session.start(agent=main_agent, room=ctx.room)
    logger.info("Enhanced e-GAP farm record agent is ready!")


if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
from datetime import date, datetime
from typing import Dict, List, Optional
import logging

from ..models import FertilizerRecord, FertilizerType, HarvestRecord, FarmRecords

class RecordHandlers:
    def __init__(self, farm_records: FarmRecords):
        self.farm_records = farm_records
        self._conversation_state = {
            "current_activity": None,
            "pending_data": {},
            "missing_fields": [],
            "awaiting_input": False,
            "record_id": None,
        }

        self.logger = logging.getLogger(__name__)

    @property
    def conversation_state(self) -> Dict:
        """Get current conversation state"""
        return self._conversation_state
    
    @property
    def pending_data(self) -> Dict:
        """Get pending data for the current activity"""
        return self._conversation_state["pending_data"]
    
    @property
    def record_id(self) -> Optional[int]:
        """Get current record ID if set"""
        return self._conversation_state.get("record_id")
    
    @record_id.setter
    def record_id(self, value: int):
        """Set record ID"""
        self._conversation_state["record_id"] = value

    @property
    def current_activity(self) -> Optional[str]:
        """Get current activity being processed"""
        return self._conversation_state.get("current_activity")
    
    @current_activity.setter
    def current_activity(self, value: str):
        """Set current activity"""
        self._conversation_state["current_activity"] = value

    def _reset_conversation_state(self):
        """Reset the conversation state after a record is saved or edited."""
        self._conversation_state = {
            "current_activity": None,
            "pending_data": {},
            "missing_fields": [],
            "awaiting_input": False,
            "record_id": None,
        }

    def update_pending_data(self, **kwargs):
        """Update pending data with provided key-value pairs"""
        for key, value in kwargs.items():
            if value is not None:
                # Handle date conversion for date fields
                if key in ['application_date', 'purchase_date', 'harvest_date'] and hasattr(value, 'isoformat'):
                    self.pending_data[key] = value.isoformat()
                else:
                    self.pending_data[key] = value

    def _get_ordinal(self, number: int) -> str:
        """Convert a number to its ordinal representation."""
        if 10 <= number % 100 <= 20:
            suffix = 'th'
        else:
            suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(number % 10, 'th')
        return f"{number}{suffix}"

    def _create_fertilizer_record(self, pending: Dict) -> FertilizerRecord:
        """Create fertilizer record from pending data"""
        # Parse dates
        app_date = datetime.strptime(pending.get("application_date", date.today().isoformat()), "%Y-%m-%d").date()
        purch_date = datetime.strptime(pending.get("purchase_date", date.today().isoformat()), "%Y-%m-%d").date()

        record_id = len(self.farm_records.fertilizer_records) + 1

        return FertilizerRecord(
            record_id=record_id,
            fertilizer_type=FertilizerType(pending.get("fertilizer_type", "OTHER").lower()),
            type_formula_name=pending["fertilizer_name"],
            purchase_date=purch_date,
            source_of_purchase=pending.get("source_of_purchase", "Not specified"),
            rate_of_use=pending["quantity_kg_per_rai"],
            usage_date=app_date,
            operator_name=pending.get("applied_by", "Not specified")
        )

    async def _save_fertilizer_record(self) -> str:
        """Save fertilizer record when all information is complete"""
        try:
            pending = self.pending_data
            
            # Get usage count for acknowledgment  
            usage_count = len([r for r in self.farm_records.fertilizer_records 
                                if r.type_formula_name == pending["fertilizer_name"]])
            
            record = self._create_fertilizer_record(pending)
            self.record_id = record.record_id
            
            # Save the record
            self.farm_records.add_fertilizer_record(record)
            
            # Generate acknowledgment with ordinal count
            ordinal = self._get_ordinal(usage_count + 1)
            acknowledgment = f"(Just so you know) — this is your {ordinal} time recording {pending['fertilizer_name']} usage."
            
            # # Clear conversation state
            # self._reset_conversation_state()
            
            result = f"{acknowledgment} Would you like to record anything else?"
            self.logger.info(f"Fertilizer record saved: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error saving fertilizer record: {e}")
            return "I had trouble saving that fertilizer record. Could you try again?"


    async def _save_harvest_record(self) -> str:
        """Save harvest record when all information is complete"""
        try:
            pending = self.conversation_state["pending_data"]
            
            # harvest_count = self.farm_records.get_crop_harvest_count(pending["crop_type"])
            harvest_count = 0 # TODO: Get from db
            # total_harvest = self.farm_records.get_total_crop_production(pending["crop_type"])
            total_harvest = 0 # TODO: Get from db
            
            # Parse harvest date
            h_date = datetime.strptime(pending.get("harvest_date", date.today().isoformat()), "%Y-%m-%d").date()
            

            record_id = len(self.farm_records.harvest_records) + 1
            self.conversation_state["record_id"] = record_id

            record = HarvestRecord(
                record_id=record_id,
                crop_type=pending["crop_type"],
                quantity_kg=pending["harvest_quantity"],
                harvest_date=h_date,
                harvested_by=pending.get("harvest_by", "self"),
                buyer_name=pending.get("buyer_name"),
                transport_vehicle=pending.get("transport_vehicle"),
                selling_price_per_kg=pending.get("selling_price_per_kg")
            )
            
            self.farm_records.add_harvest_record(record)
            
            # Generate acknowledgment following script pattern
            ordinal = self._get_ordinal(harvest_count + 1)
            new_total = total_harvest + pending["harvest_quantity"]
            
            if harvest_count >= 2:  # Third harvest or more
                acknowledgment = f"Thanks — this is your {ordinal} harvest. So far, your total {pending['crop_type']} production is {new_total} kg."
            else:
                acknowledgment = f"{'Okay, noted' if harvest_count == 0 else 'Got it'} — this is your {ordinal} {pending['crop_type']} harvest."
            
            # Clear conversation state
            self._reset_conversation_state()
            
            return acknowledgment
            
        except Exception as e:
            self.logger.error(f"Error saving harvest record: {e}")
            return "I had trouble saving that harvest record. Could you try again?"
            
    async def _edit_fertilizer_record(self) -> str:
        """Edit fertilizer record when all information is complete"""
        try:
            pending = self.pending_data
            record_id = self.record_id
            
            # Get existing record to edit
            existing_record = self.farm_records.get_fertilizer_record_by_id(record_id)
            if not existing_record:
                return "I couldn't find the fertilizer record to edit. Please record a new fertilizer usage."
            
            # Parse dates
            app_date = datetime.strptime(pending.get("application_date", date.today().isoformat()), "%Y-%m-%d").date()
            purch_date = datetime.strptime(pending.get("purchase_date", date.today().isoformat()), "%Y-%m-%d").date()
            
            # Update existing record with new data
            existing_record.fertilizer_type = FertilizerType(pending.get("fertilizer_type", "OTHER").lower())
            existing_record.type_formula_name = pending["fertilizer_name"]
            existing_record.purchase_date = purch_date
            existing_record.source_of_purchase = pending.get("source_of_purchase", "Not specified")
            existing_record.rate_of_use = pending["quantity_kg_per_rai"]
            existing_record.usage_date = app_date
            existing_record.operator_name = pending.get("applied_by", "Not specified")
            
            # Save the updatesd record
            updated = self.farm_records.update_fertilizer_record(record_id, existing_record)
            if not updated:
                return "I couldn't update that fertilizer record. Please try again."
            
            return f"Your fertilizer record for {pending['fertilizer_name']} has been updated successfully. Would you like to record anything else?"
        except Exception as e:
            self.logger.error(f"Error editing fertilizer record: {e}")
            return "I had trouble updating that fertilizer record. Could you try again?"
            
    async def _edit_harvest_record(self) -> str:
        """Edit harvest record when all information is complete"""
        try:
            pending = self.pending_data
            record_id = self.record_id
            
            # Get existing record to edit
            existing_record = self.farm_records.get_harvest_record_by_id(record_id)
            if not existing_record:
                return "I couldn't find the harvest record to edit. Please record a new harvest."
            
            # Parse harvest date
            h_date = datetime.strptime(pending.get("harvest_date", date.today().isoformat()), "%Y-%m-%d").date()
            # Update existing record with new data
            existing_record.crop_type = pending["crop_type"]
            existing_record.quantity_kg = pending["harvest_quantity"]
            existing_record.harvest_date = h_date
            existing_record.harvested_by = pending.get("harvest_by", "self")
            existing_record.buyer_name = pending.get("buyer_name")
            existing_record.transport_vehicle = pending.get("transport_vehicle")
            existing_record.selling_price_per_kg = pending.get("selling_price_per_kg")
            # Save the updated record
            updated = self.farm_records.update_harvest_record(record_id, existing_record)
            if not updated:
                return "I couldn't update that harvest record. Please try again."
            return f"Your harvest record for {pending['crop_type']} has been updated successfully. Would you like to record anything else?"
        except Exception as e:
            self.logger.error(f"Error editing harvest record: {e}")
            return "I had trouble updating that harvest record. Could you try again?"
        
        

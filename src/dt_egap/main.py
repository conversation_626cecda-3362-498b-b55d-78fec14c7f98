import os

import hydra
import hydra_zen
import typer
from livekit import agents
from livekit.agents.cli import _run, cli, proto

from dt_egap.agents.agent_multi_tool import MainFarmAgent
from dt_egap.conf import Conf, store

conf_zen = hydra_zen.zen(Conf)

typer_app = typer.Typer()


async def _entrypoint(ctx: agents.JobContext):
    store.add_to_hydra_store()
    with hydra.initialize(config_path=None, version_base=None):
        _conf = hydra.compose(config_name=Conf.__name__)
    conf = conf_zen(_conf)

    sessison = conf.session_partial()

    # room_io = agents.RoomIO(session, room=ctx.room)
    # await room_io.start()

    await sessison.start(
        agent=MainFarmAgent(),
        room=ctx.room,
        # room_output_options=agents.RoomOutputOptions(),
        # room_output_options=agents.RoomOutputOptions(),
    )


@typer_app.callback()
def callback():
    pass


@typer_app.command()
def dev(
    log_level: str = "DEBUG",
    asyncio_debug: bool = False,
    watch: bool = True,
    overrides: list[str] | None = None,
):
    opts = agents.WorkerOptions(
        entrypoint_fnc=_entrypoint,
        #
        ws_url=os.environ["LIVEKIT_URL"],
        api_key=os.environ["LIVEKIT_API_KEY"],
        api_secret=os.environ["LIVEKIT_API_SECRET"],
        # api_key =os.environ["LIVEKIT_API_KEY"],
        drain_timeout=0,
    )
    args = proto.CliArgs(
        opts=opts,
        log_level=log_level,
        devmode=True,
        asyncio_debug=asyncio_debug,
        watch=watch,
        register=True,
    )
    cli.CLI_ARGUMENTS = args
    _run.run_dev(args)


@typer_app.command()
def console(overrides: list[str] | None = None):
    opts = agents.WorkerOptions(
        entrypoint_fnc=_entrypoint,
        #
        job_executor_type=agents.JobExecutorType.THREAD,
        drain_timeout=0,
        api_key="empty",
        api_secret="empty",
    )
    args = proto.CliArgs(
        opts=opts,
        log_level="DEBUG",
        devmode=True,
        asyncio_debug=False,
        watch=False,
        console=True,
        # record=record,
        register=False,
        simulate_job=agents.SimulateJobInfo(room="mock-console"),
    )
    cli.CLI_ARGUMENTS = args
    _run.run_worker(args)


if __name__ == "__main__":
    store.add_to_hydra_store()
    typer_app()

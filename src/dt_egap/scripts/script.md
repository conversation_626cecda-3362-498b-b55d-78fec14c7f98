# e-GAP CHATBOT SCRIPT

## SCENARIO 1: FERTILIZER/ORGANIC MATTER USAGE RECORDING

**Screen:** Home screen
**Context:** User opens the e-GAP web app for the first time

**e-GAP:** Good morning! What would you like to record today?

**Suggestion cards displayed:**
- Fertilizer usage
- Agricultural chemical usage  
- Harvesting

**User:** I applied compost (manure) at my local farm today.

**e-GAP:** When did you buy it? How much did you use? And who applied it?

**User:** I bought it last week and <PERSON><PERSON><PERSON> used 1000 kg/rai.

**e-GAP:** (Just so you know) — this is your first time recording compost usage. Would you like to record anything else?

---

## SCENARIO 2: AGRICULTURAL CHEMICAL USAGE RECORDING

**User:** I bought Gibberellic acid (GA3) today from Krabi Greenhouse. Application rate is 0.05.

**e-GAP:** Who will apply it? And when will he apply it?

**User:** <PERSON><PERSON><PERSON><PERSON> will apply it tomorrow.

**Action:** Show 8.1 - Check history (advanced)

**e-GAP:** This is your second time recording GA3 usage. I've saved the details to your e-GAP log.

---

## SCENARIO 3: FIRST HARVEST RECORDING

**Screen:** Home screen
**Context:** Next 3 months, the user inputs harvesting information

**e-GAP:** What else would you like to record today?

**User:** I just harvested 1,200 kg of durian today.

**e-GAP:** Did you harvest it yourself?

**User:** No, the buyer did the harvesting.

**Action:** Show 9.1

**e-GAP:** Okay, noted — this is your first durian harvest.

---

## SCENARIO 4: SECOND HARVEST RECORDING

**Screen:** Home screen  
**Context:** Next 1 month, the user inputs harvesting information

**e-GAP:** What would you like to record today?

**User:** I harvested 1,000 kg of durian myself today.

**Action:** Show 9.2

**e-GAP:** Got it — this is your second durian harvest.

**User:** Yes.

---

## SCENARIO 5: THIRD HARVEST RECORDING WITH SALES INFORMATION

**Screen:** Home screen
**Context:** Next 1 month, the user inputs harvesting information

**e-GAP:** What would you like to record today?

**User:** 1,500 kg of durian was harvested by hired labor today.

**Action:** Show 9.3, 9.4

**e-GAP:** Thanks — this is your third harvest. So far, your total durian production is 3,700 kg.

**User:** Yes.

**e-GAP:** Who is the buyer? What vehicle was used for transport?

**User:** Chaiyaporn Export Co., Ltd. used the truck to deliver durian.

**Action:** Show 9.7

**e-GAP:** What is the Selling Price (THB per fruit/kg)?

**User:** 120 THB/kg.

---

## SCENARIO 6: COMPLETION AND e-GAP FORM REVIEW

**Action:** Show the e-GAP form

**e-GAP:** Your data is complete and ready for e-GAP submission. Would you like to review your e-GAP?

**User:** Yes, I've reviewed it. Everything looks correct.

---

## ADDITIONAL NOTES

**Features:**
- Automatic tracking of recording instances for each activity type
- History storage and cumulative statistics display
- Integration with official e-GAP forms
- Support for multiple input types: fertilizers, chemicals, harvesting
- Detailed information tracking: applicator, timing, quantities

**Data Collected:**
- Fertilizer/Chemical information: product name, purchase source, quantity, applicator, timing
- Harvest information: quantity, harvester, timing
- Sales information: buyer, selling price, transport vehicle
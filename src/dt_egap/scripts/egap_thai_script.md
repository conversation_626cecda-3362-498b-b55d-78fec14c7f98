# e-GAP CHATBOT SCRIPT
## SCENARIO 1: FERTILIZER/ORGANIC MATTER USAGE RECORDING

**Screen:** Home screen

**Context:** User opens the e-GAP web app for the first time

**e-GAP:** สวัสดีตอนเช้า! วันนี้คุณต้องการบันทึกอะไรบ้าง?

**Suggestion cards displayed:**
- Fertilizer usage
- Agricultural chemical usage
- Harvesting

**User:** สวัสดี

**e-GAP:** สวัสดีครับ! ยินดีที่ได้พบกันนะครับ

nวันนี้มีกิจกรรมอะไรในไร่หรือสวนที่อยากจะบันทึกไว้ไหมครับ? 

เช่น การใส่ปุ๋ย การฉีดยา หรือการเก็บเกี่ยวผลผลิต อะไรก็ได้เลยครับ

**User:** วันนี้ผมได้ใส่ปุ่ยหมัก (มูลสัตว์) ที่ไร่ในพื้นที่แล้ว

**e-GAP:** ซื้อเมื่อไหร่? ใช้เท่าไหร่? และใครเป็นคนใส่?

**User:** ซื้อเมื่อสัปดาห์ที่แล้ว และบรรชัยใส่ 1,000 กิโลกรัมต่อไร่

**e-GAP:** (แจ้งให้ทราบ) — นี่เป็นครั้งแรกที่คุณบันทึกการใช้ปุ่ยหมัก คุณต้องการบันทึกอะไรอื่นอีกไหม?

---
## SCENARIO 2: AGRICULTURAL CHEMICAL USAGE RECORDING
**User:** วันนี้ผมซื้อ Gibberellic acid (GA3) จากกระบี่เกรนเฮาส์ อัตราการใช้ 0.05

**e-GAP:** ใครจะเป็นคนใส่? และเมื่อไหร่ที่จะใส่?

**User:** สมชายใส่เมื่อพรุ่งนี้

**Action:** Show 8.1 - Check history (advanced)

**e-GAP:** นี่เป็นครั้งที่สองที่คุณบันทึกการใช้ GA3 ข้อมูลได้ถูกบันทึกในบันทึก e-GAP ของคุณแล้ว

---
## SCENARIO 3: FIRST HARVEST RECORDING
**Screen:** Home screen

**Context:** Next 3 months, the user inputs harvesting information

**e-GAP:** วันนี้คุณต้องการบันทึกอะไรอื่นอีกบ้าง?

**User:** วันนี้ผมเพิ่งเก็บทุเรียนได้ 1,200 กิโลกรัม

**e-GAP:** คุณเก็บเองหรือไม่?

**User:** ไม่ครับ คนซื้อเป็นคนเก็บ

**Action:** Show 9.1

**e-GAP:** โอเค จดแล้ว — นี่เป็นการเก็บทุเรียนครั้งแรกของคุณ

---
## SCENARIO 4: SECOND HARVEST RECORDING
**Screen:** Home screen

**Context:** Next 1 month, the user inputs harvesting information

**e-GAP:** วันนี้คุณต้องการบันทึกอะไรบ้าง?

**User:** วันนี้ผมเก็บทุเรียนเอง 1,000 กิโลกรัม

**Action:** Show 9.2

**e-GAP:** เข้าใจแล้ว — นี่เป็นการเก็บทุเรียนครั้งที่สองของคุณ

**User:** ใช่ครับ

---
## SCENARIO 5: THIRD HARVEST RECORDING WITH SALES INFORMATION
**Screen:** Home screen

**Context:** Next 1 month, the user inputs harvesting information

**e-GAP:** วันนี้คุณต้องการบันทึกอะไรบ้าง?

**User:** วันนี้มีการเก็บทุเรียน 1,500 กิโลกรัม โดยจ้างคนงาน

**Action:** Show 9.3, 9.4

**e-GAP:** ขอบคุณ — นี่เป็นการเก็บเกี่ยวครั้งที่สาม รวมผลผลิทุเรียนทั้งหมดของคุณคือ 3,700 กิโลกรัม

**User:** ใช่ครับ

**e-GAP:** ผู้ซื้อคือใคร? ใช้พาหนะอะไรในการขนส่ง?

**User:** บริษัท ไชยพร เอ็กซ์ปอร์ต จำกัด ใช้รถบรรทุกขนส่งทุเรียน

**Action:** Show 9.7

**e-GAP:** ราคาขายเท่าไหร่ (บาทต่อผล/กิโลกรัม)?

**User:** 120 บาทต่อกิโลกรัม

---
## SCENARIO 6: COMPLETION AND e-GAP FORM REVIEW

**Action:** Show the e-GAP form

**e-GAP:** ข้อมูลของคุณครบถ้วนและพร้อมสำหรับการส่ง e-GAP แล้ว คุณต้องการตรวจสอบ e-GAP ของคุณไหม?

**User:** ครับ ผมตรวจสอบแล้ว ทุกอย่างถูกต้อง

---
## ADDITIONAL NOTES
**Features:**
- Automatic tracking of recording instances for each activity type
- History storage and cumulative statistics display
- Integration with official e-GAP forms
- Support for multiple input types: fertilizers, chemicals, harvesting
- Detailed information tracking: applicator, timing, quantities

**Data Collected:**
- Fertilizer/Chemical information: product name, purchase source, quantity, applicator, timing
- Harvest information: quantity, harvester, timing
- Sales information: buyer, selling price, transport vehicle
import asyncio
# import sys

# from pathlib import Path

# # Add project root to Python path
# project_root = Path(__file__).resolve().parent.parent.parent
# sys.path.insert(0, str(project_root))

from livekit.plugins import aws
from livekit.agents import AgentSession
from livekit.agents.llm import FunctionCallOutput
from dt_egap.agents import SchemaFillingAgent  # Your agent
from dt_egap.agents.agent_multi_tool import MainFarmAgent
import os

os.environ['AWS_PROFILE'] = os.getenv('AWS_PROFILE', 'default')


class RealAgentChat:
    def __init__(self):
        self.session = None
        self.agent = None
        self.llm = None
        
    async def setup_agent(self):
        """Setup real agent - no mocks"""
        print("🔧 Setting up real agent...")
        
        # Create LLM
        # self.llm = aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0")
        # self.llm = aws.LLM(model="anthropic.claude-3-5-sonnet-20240620-v1:0")
        self.llm = aws.LLM(
            model="apac.anthropic.claude-sonnet-4-20250514-v1:0",
            region="ap-southeast-1"  # Hoặc region APAC khác như ap-northeast-1
        )
        await self.llm.__aenter__()
        
        # Create session
        self.session = AgentSession(llm=self.llm)
        await self.session.__aenter__()
        
        # Create real agent
        # self.agent = SchemaFillingAgent()
        self.agent = MainFarmAgent()
        
        # Start session with real agent
        await self.session.start(self.agent)
        
        print("✅ Real agent ready!")
        
    async def chat_loop(self):
        """Simple chat loop với real agent"""
        print("\n" + "="*50)
        print("🤖 Real Agent Chat - Type 'quit' to exit")
        print("="*50)
        print("Chat with the actual agent!")
        print("="*50 + "\n")
        
        while True:
            try:
                # Get user input
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                    break
                    
                if not user_input:
                    continue
                    
                print("\n🤖 Agent thinking...\n")
                
                # Send to real agent
                result = await self.session.run(user_input=user_input)
                
                # Show what happened
                print("📋 Session Events:")
                print("-" * 40)
                
                for i, event in enumerate(result.events, 1):
                    event_type = type(event).__name__
                    print(f"{i}. {event_type}")
                    
                    if hasattr(event, 'item'):
                        item = event.item

                        # ## Get all attributes of the item
                        # attributes = [attr for attr in dir(item) if not attr.startswith('_')]
                        # print(f"   Attributes: {attributes}")
                        
                        # Function call
                        if hasattr(item, 'name'):
                            print(f"   🔧 Function: {item.name}")
                            if hasattr(item, 'arguments'):
                                print(f"   📝 Args: {item.arguments}")
                                
                        # Function result  

                        ## check funtioncalling output event
                        if type(item) == FunctionCallOutput:
                            print(f"   ✅ Function Output: {item.output}")

                        # elif hasattr(item, 'output'):
                        #     print(f"   ✅ Output: {item.output}")
                            
                        # Message
                        elif hasattr(item, 'content'):
                            
                            print("Content:", item.content)

                            role = getattr(item, 'role', 'unknown')
                            content = item.content
                            
                            if role == 'user':
                                print(f"   👤 User: {content}")
                            elif role == 'assistant':
                                print(f"   🤖 Assistant: {content}")
                            else:
                                print(f"   📄 {role}: {content}")
                
                # Extract final assistant response
                assistant_messages = [
                    e for e in result.events 
                    if hasattr(e, 'item') and hasattr(e.item, 'role') and e.item.role == 'assistant'
                ]
                
                print("-" * 40)
                if assistant_messages:
                    final_response = assistant_messages[-1].item.content
                    print(f"\n🎯 FINAL AGENT RESPONSE:")
                    print(f"{final_response}")
                else:
                    print("\n⚠️  No assistant response found")
                
                print("\n" + "="*50 + "\n")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                print("Final farm records state:")
                print(self.agent.farm_records)
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                import traceback
                traceback.print_exc()
                print()
                
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
            if self.llm:
                await self.llm.__aexit__(None, None, None)
        except Exception as e:
            print(f"Cleanup error: {e}")


async def main():
    """Main function"""
    chat = RealAgentChat()
    
    try:
        await chat.setup_agent()
        await chat.chat_loop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await chat.cleanup()
        print("Final farm records state:")
        print(chat.agent.farm_records)
        print("👋 Chat ended.")


if __name__ == "__main__":
    asyncio.run(main())
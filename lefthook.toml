[pre-commit]
parallel = true

[[pre-commit.jobs]]
glob = "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
run = "biome check --write --no-errors-on-unmatched --files-ignore-unknown=true --colors=off {staged_files}"
stage_fixed = true

[[pre-commit.jobs]]
glob = "justfile"
run = "just --fmt"
stage_fixed = true

[[pre-commit.jobs]]
glob = "*.toml"
run = "taplo format {staged_files}"
stage_fixed = true

[[pre-commit.jobs]]
glob = "*.{py,pyi,ipynb}"
stage_fixed = true

[pre-commit.jobs.group]
piped = true

[[pre-commit.jobs.group.jobs]]
run = "ruff check --fix --force-exclude {staged_files}"

[[pre-commit.jobs.group.jobs]]
run = "ruff format --force-exclude {staged_files}"

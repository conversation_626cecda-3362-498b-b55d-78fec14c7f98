# DT

## Folder structures
### src/dt_egap
- agents: TODO Agent implementation
- main.py: WIP LiveKit main entrypoint
- providers: WIP configurations switching using 

### Code Convention
- hk.pkl: pre-commit hooks
- justfile: commands entrypoint
- mise.toml: tools management

<!-- ## Run
In two terminal tabs
```
just edge_tts
just transformers_serve
```

## To test this draft
In two terminal tabs
```
just tts_serve
just transformers_serve
``` -->

Main command
```shell
uv run src/dt_egap/main.py console
```
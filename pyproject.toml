[project]
name = "dt-egap"
version = "0.1.0"
description = "TODO"
readme = "README.md"
authors = [{ name = "<PERSON><PERSON>" }]
requires-python = ">=3.12"
dependencies = [
  "boto3>=1.34.0",
  "dotenv>=0.9.9",
  "hydra-zen>=0.15.0",
  "livekit-agents[aws,google,openai,silero]>=1.2.5",
  "openpyxl>=3.0.0",
  "pandas>=2.0.0",
  "pytest>=8.4.1",
  "typer-slim>=0.16.0",
  "xlsxwriter>=3.0.0",
  "loguru>=0.7.0",
]

[build-system]
requires = ["uv_build>=0.8.5,<0.9.0"]
build-backend = "uv_build"

[dependency-groups]
dev = [
  "pytest-asyncio>=1.1.0",
  "torch>=2.8.0",
  "torchaudio>=2.4.0",
  "aiobotocore>=2.13.0",
  "python-multipart>=0.0.20",
  "transformers[audio,serving]>=4.55.0",
]

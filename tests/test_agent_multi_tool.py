# test_agent_real.py
import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
from livekit.agents import AgentSession
from livekit.plugins import aws
from dt_egap.agents.agent_multi_tool import MainFarmAgent
from dt_egap.models import FertilizerType

# Set AWS profile nếu cần
os.environ['AWS_PROFILE'] = os.getenv('AWS_PROFILE', 'default')

@pytest.mark.asyncio
async def test_agent_session_basic():
    """Test cơ bản với AgentSession"""
    
    try:
        async with aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0") as llm:
            print(f"✅ LLM created: {llm}")
            
            async with AgentSession(llm=llm) as session:
                print(f"✅ Session created: {session}")
                
                # Tạo agent và mock dependencies sau khi khởi tạo
                agent = MainFarmAgent()
                print(f"✅ Agent created: {agent}")
                
                # Mock FarmRecords methods  
                agent.farm_records.get_fertilizer_usage_count = Mock(return_value=0)
                agent.farm_records.add_fertilizer_record = Mock()
                agent.farm_records.get_chemical_usage_count = Mock(return_value=0) 
                agent.farm_records.add_chemical_record = Mock()
                agent.farm_records.get_crop_harvest_count = Mock(return_value=0)
                agent.farm_records.add_harvest_record = Mock()
            
                
                await session.start(agent)
                print("✅ Session started")
                
                # Test conversation  
                result = await session.run(user_input="I used 15 kg of compost fertilizer")
                print(f"✅ Session run completed")
                
                # Basic validation
                assert result is not None
                print(f"✅ Got {len(result.events)} events from session")
                
                # Check events
                for i, event in enumerate(result.events):
                    print(f"Event {i}: {type(event).__name__}")
                    
                    # If it's a function call event
                    if hasattr(event, 'item') and hasattr(event.item, 'name'):
                        print(f"  Function: {event.item.name}")
                        if hasattr(event.item, 'arguments'):
                            print(f"  Arguments: {event.item.arguments}")
                        
                    # If it's a function output event
                    elif hasattr(event, 'item') and hasattr(event.item, 'output'):
                        function_output = event.item.output
                        print(f"  Function Output: {function_output}")
                        
                        # Debug: Call function directly to see what it returns
                        direct_result = await agent.analyze_and_record_farm_activity(Mock(), "I used 15 kg of compost fertilizer")
                        print(f"  Direct function result: {direct_result}")
                        
                    # If it's a message
                    elif hasattr(event, 'item') and hasattr(event.item, 'content'):
                        print(f"  Message: {event.item.content[:100]}...")
                        
                # Check if final ChatMessage exists
                final_messages = [e for e in result.events if hasattr(e, 'item') and hasattr(e.item, 'role') and e.item.role == 'assistant']
                print(f"Assistant messages count: {len(final_messages)}")
                if len(final_messages) > 1:
                    print(f"Final message: {final_messages[-1].item.content}")
                else:
                    print("⚠️ Missing final assistant message - LLM didn't generate response after function call")
                
                # Verify function was called
                agent.farm_records.add_fertilizer_record.assert_called()
                print("✅ Functions called as expected")
    
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        traceback.print_exc()
        raise
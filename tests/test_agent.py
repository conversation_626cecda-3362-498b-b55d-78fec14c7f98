# test_agent_real.py
import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
from livekit.agents import AgentSession
from livekit.plugins import aws
from dt_egap.agents import SchemaFillingAgent
from dt_egap.models import FertilizerType

# Set AWS profile nếu cần
os.environ['AWS_PROFILE'] = os.getenv('AWS_PROFILE', 'default')

# @pytest.mark.asyncio
# async def test_agent_session_basic():
#     """Test cơ bản với AgentSession"""
    
#     try:
#         async with aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0") as llm:
#             print(f"✅ LLM created: {llm}")
            
#             async with AgentSession(llm=llm) as session:
#                 print(f"✅ Session created: {session}")
                
#                 # Tạo agent và mock dependencies sau khi khởi tạo
#                 agent = SchemaFillingAgent()
#                 print(f"✅ Agent created: {agent}")
                
#                 # Mock FarmRecords methods  
#                 agent.farm_records.get_fertilizer_usage_count = Mock(return_value=0)
#                 agent.farm_records.add_fertilizer_record = Mock()
#                 agent.farm_records.get_chemical_usage_count = Mock(return_value=0) 
#                 agent.farm_records.add_chemical_record = Mock()
#                 agent.farm_records.get_crop_harvest_count = Mock(return_value=0)
#                 agent.farm_records.add_harvest_record = Mock()
                
#                 # Mock DataExtractor
#                 mock_extracted = Mock()
#                 mock_extracted.extraction_type = "fertilizer"
#                 mock_extracted.confidence_score = 0.9
#                 mock_extracted.fertilizer_type = "ORGANIC"  # Use enum value directly
#                 mock_extracted.fertilizer_name = "compost"
#                 mock_extracted.fertilizer_quantity_kg_per_rai = 15.0
#                 mock_extracted.fertilizer_purchase_date = None
#                 mock_extracted.fertilizer_application_date = None
#                 mock_extracted.fertilizer_applied_by = None
                
#                 # Debug mock object
#                 print(f"Mock fertilizer_name: {mock_extracted.fertilizer_name}")
#                 print(f"Mock quantity: {mock_extracted.fertilizer_quantity_kg_per_rai}")
                
#                 agent.unified_extractor.extract_all_farm_data = AsyncMock(return_value=mock_extracted)
#                 print("✅ Agent dependencies mocked")
                
#                 await session.start(agent)
#                 print("✅ Session started")
                
#                 # Test conversation  
#                 result = await session.run(user_input="I used 15 kg of compost fertilizer")
#                 print(f"✅ Session run completed")
                
#                 # Basic validation
#                 assert result is not None
#                 print(f"✅ Got {len(result.events)} events from session")
                
#                 # Check events
#                 for i, event in enumerate(result.events):
#                     print(f"Event {i}: {type(event).__name__}")
                    
#                     # If it's a function call event
#                     if hasattr(event, 'item') and hasattr(event.item, 'name'):
#                         print(f"  Function: {event.item.name}")
#                         if hasattr(event.item, 'arguments'):
#                             print(f"  Arguments: {event.item.arguments}")
                        
#                     # If it's a function output event
#                     elif hasattr(event, 'item') and hasattr(event.item, 'output'):
#                         function_output = event.item.output
#                         print(f"  Function Output: {function_output}")
                        
#                         # Debug: Call function directly to see what it returns
#                         direct_result = await agent.analyze_and_record_farm_activity(Mock(), "I used 15 kg of compost fertilizer")
#                         print(f"  Direct function result: {direct_result}")
                        
#                     # If it's a message
#                     elif hasattr(event, 'item') and hasattr(event.item, 'content'):
#                         print(f"  Message: {event.item.content[:100]}...")
                        
#                 # Check if final ChatMessage exists
#                 final_messages = [e for e in result.events if hasattr(e, 'item') and hasattr(e.item, 'role') and e.item.role == 'assistant']
#                 print(f"Assistant messages count: {len(final_messages)}")
#                 if len(final_messages) > 1:
#                     print(f"Final message: {final_messages[-1].item.content}")
#                 else:
#                     print("⚠️ Missing final assistant message - LLM didn't generate response after function call")
                
#                 # Verify function was called
#                 agent.unified_extractor.extract_all_farm_data.assert_called()
#                 agent.farm_records.add_fertilizer_record.assert_called()
#                 print("✅ Functions called as expected")
    
#     except Exception as e:
#         print(f"❌ Error in test: {e}")
#         import traceback
#         traceback.print_exc()
#         raise


@pytest.mark.asyncio
async def test_agent_conversation_flow():
    """Test cơ bản với conversation flow của agent"""
    
    try:
        async with aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0") as llm:
            print(f"✅ LLM created: {llm}")
            
            async with AgentSession(llm=llm) as session:
                print(f"✅ Session created: {session}")
                
                # Tạo agent và mock dependencies sau khi khởi tạo
                agent = SchemaFillingAgent()
                print(f"✅ Agent created: {agent}")
                
                await session.start(agent)
                print("✅ Session started")
                
                # Test conversation  
                result = await session.run(user_input="I applied compost(manure) at my local farm today.")
                print(f"✅ Session run completed")
                
                # Basic validation
                assert result is not None
                print(f"✅ Got {len(result.events)} events from session")
                
                # Check events
                for i, event in enumerate(result.events):
                    print(f"Event {i}: {type(event).__name__}")
                    
                    # If it's a function call event
                    if hasattr(event, 'item') and hasattr(event.item, 'name'):
                        print(f"  Function: {event.item.name}")
                        if hasattr(event.item, 'arguments'):
                            print(f"  Arguments: {event.item.arguments}")
                        
                    # If it's a function output event
                    elif hasattr(event, 'item') and hasattr(event.item, 'output'):
                        function_output = event.item.output
                        print(f"  Function Output: {function_output}")
                        
                        # Debug: Call function directly to see what it returns
                        direct_result = await agent.analyze_and_record_farm_activity(Mock(), "I used 15 kg of compost fertilizer")
                        print(f"  Direct function result: {direct_result}")
                        
                    # If it's a message
                    elif hasattr(event, 'item') and hasattr(event.item, 'content'):
                        print(f"  Message: {event.item.content[:100]}...")
                        
                # Check if final ChatMessage exists
                final_messages = [e for e in result.events if hasattr(e, 'item') and hasattr(e.item, 'role') and e.item.role == 'assistant']
                print(f"Assistant messages count: {len(final_messages)}")
                if len(final_messages) > 1:
                    print(f"Final message: {final_messages[-1].item.content}")
                else:
                    print("⚠️ Missing final assistant message - LLM didn't generate response after function call")
                
                # Verify function was called
                agent.unified_extractor.extract_all_farm_data.assert_called()
                agent.farm_records.add_fertilizer_record.assert_called()
                print("✅ Functions called as expected")
    
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        traceback.print_exc()
        raise



# @pytest.mark.asyncio
# async def test_function_called_directly():
#     """Test function trực tiếp để verify logic"""
    
#     agent = SchemaFillingAgent()
    
#     # Mock dependencies
#     agent.farm_records.get_fertilizer_usage_count = Mock(return_value=0)
#     agent.farm_records.add_fertilizer_record = Mock()
    
#     mock_extracted = Mock()
#     mock_extracted.extraction_type = "fertilizer"
#     mock_extracted.confidence_score = 0.9
#     mock_extracted.fertilizer_name = "compost"
#     mock_extracted.fertilizer_quantity_kg_per_rai = 15.0
#     mock_extracted.fertilizer_purchase_date = None
#     mock_extracted.fertilizer_application_date = None
#     mock_extracted.fertilizer_applied_by = None
    
#     agent.unified_extractor.extract_all_farm_data = AsyncMock(return_value=mock_extracted)
    
#     # Test function call
#     context = Mock()
#     result = await agent.analyze_and_record_farm_activity(context, "I used 15 kg of compost")
    
#     # Verify function was called properly
#     agent.unified_extractor.extract_all_farm_data.assert_called_once_with("I used 15 kg of compost")
#     agent.farm_records.add_fertilizer_record.assert_called_once()
    
#     # Verify result content
#     assert "compost" in result.lower()
#     assert "15" in result
    
#     print("✅ Direct function test passed!")


# @pytest.mark.asyncio
# async def test_llm_with_function_tools():
#     """Test LLM có call được function tools không"""
    
#     # Tạo simple test agent với function tool
#     from livekit.agents import Agent, function_tool, RunContext
    
#     call_log = []
    
#     class TestAgent(Agent):
#         def __init__(self):
#             super().__init__(instructions="You are a test agent")
        
#         @function_tool
#         async def test_function(self, ctx: RunContext, message: str) -> str:
#             """Test function that logs calls"""
#             call_log.append(message)
#             return f"Processed: {message}"
    
#     async with aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0") as llm:
#         async with AgentSession(llm=llm) as session:
            
#             test_agent = TestAgent()
#             await session.start(test_agent)
            
#             # Test với input có thể trigger function
#             result = await session.run(user_input="Please process this message: Hello World")
            
#             print(f"✅ Session completed with {len(result.events)} events")
            
#             # Check nếu function được call
#             if call_log:
#                 print(f"✅ Function was called with: {call_log}")
#             else:
#                 print("⚠️ Function was not called - might need different prompt")
            
#             # Log all events để debug
#             for i, event in enumerate(result.events):
#                 event_type = type(event).__name__
#                 print(f"Event {i}: {event_type}")


# @pytest.mark.asyncio
# async def test_simple_greeting():
#     """Test đơn giản để verify session hoạt động"""
    
#     async with aws.LLM(model="anthropic.claude-3-haiku-20240307-v1:0") as llm:
#         async with AgentSession(llm=llm) as session:
            
#             from livekit.agents import Agent
#             simple_agent = Agent(instructions="You are a friendly assistant. Respond briefly.")
            
#             await session.start(simple_agent)
            
#             result = await session.run(user_input="Hello, how are you?")
            
#             assert result is not None
#             assert len(result.events) > 0
            
#             # Find assistant message
#             assistant_messages = []
#             for event in result.events:
#                 if hasattr(event, 'item') and hasattr(event.item, 'role'):
#                     if event.item.role == 'assistant':
#                         assistant_messages.append(event.item.content)
            
#             if assistant_messages:
#                 print(f"✅ Assistant replied: {assistant_messages[0]}")
#             else:
#                 print("⚠️ No assistant message found")
            
#             print(f"✅ Basic greeting test passed with {len(result.events)} events")


# Run tests
if __name__ == "__main__":
    async def run_all_tests():
        print("🧪 Running LiveKit Agent Tests...")
        
        # await test_function_called_directly()
        # await test_simple_greeting() 
        # await test_llm_with_function_tools()
        await test_agent_session_basic()
        
        print("✅ All tests completed!")
    
    asyncio.run(run_all_tests())
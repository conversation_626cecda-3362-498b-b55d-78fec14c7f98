# AWS Configuration
AWS_PROFILE=default

# Edge TTS Configuration  
REQUIRE_API_KEY=False

# LiveKit Configuration (if needed)
# LIVEKIT_API_KEY=your_livekit_api_key
# LIVEKIT_API_SECRET=your_livekit_api_secret
# LIVEKIT_URL=wss://your-livekit-server.com

# Bedrock/AWS Configuration
# AWS_REGION=ap-southeast-1
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# GOOGLE Configuration
# GOOGLE_CRE=
# GOOGLE_CRE_FILEs=

# Application Configuration
LANGUAGE=en # en, th
# DEBUG_IN_ENG=True
# DEBUG=True
# LOG_LEVEL=INFO